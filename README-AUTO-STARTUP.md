# TimeController 开机自启动和安全防护功能

## 功能概述

本次更新为TimeController应用添加了以下两个重要功能：

### 1. 开机自启动功能
- ✅ 设备开机后自动启动应用核心服务
- ✅ 支持多种开机场景（正常开机、快速开机、应用更新）
- ✅ 智能权限检查和服务恢复
- ✅ 延迟启动机制，确保系统稳定

### 2. Release版本安全防护
- ✅ Release版本自动隐藏最近任务
- ✅ Debug版本保持可见，方便调试
- ✅ 多层防护机制（Manifest + 运行时）
- ✅ 防止用户意外关闭应用

## 实现的文件

### 新增文件
1. **`app/src/main/java/com/hwb/timecontroller/receiver/BootReceiver.kt`**
   - 开机启动广播接收器
   - 处理开机完成、快速开机、应用更新等事件
   - 智能启动核心服务

2. **`app/src/test/java/com/hwb/timecontroller/receiver/BootReceiverTest.kt`**
   - BootReceiver的单元测试
   - 验证各种启动场景

3. **`.docs/features/auto-startup-and-security.md`**
   - 详细的功能文档
   - 实现原理和故障排除指南

### 修改的文件
1. **`app/src/main/AndroidManifest.xml`**
   - 添加开机启动权限
   - 注册BootReceiver
   - 配置MainActivity的excludeFromRecents属性

2. **`app/build.gradle.kts`**
   - 配置不同构建类型的manifest占位符
   - Debug版本显示最近任务，Release版本隐藏

3. **`app/src/main/java/com/hwb/timecontroller/activity/MainActivity.kt`**
   - 添加Release版本隐藏最近任务的逻辑
   - 运行时动态设置excludeFromRecents

## 权限要求

### 新增权限
```xml
<!-- 开机自启动权限 -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

### 前置条件
- **设备所有者权限**：必须具有Device Owner权限
- **无障碍服务**：需要用户手动启用无障碍服务
- **悬浮窗权限**：会自动通过设备所有者权限获取

## 使用方法

### 开机自启动
1. 确保应用已获得设备所有者权限
2. 启用无障碍服务
3. 重启设备
4. 应用会在开机后3秒自动启动核心服务

### 隐藏最近任务
1. 构建Release版本的APK
2. 安装并启动应用
3. 应用将自动从最近任务列表中隐藏

## 技术特点

### 开机自启动
- **延迟启动**：开机后延迟3秒启动，确保系统稳定
- **权限验证**：启动前验证设备所有者权限
- **智能恢复**：自动检查并启动必要的服务
- **异常处理**：完善的错误处理和日志记录

### 安全防护
- **构建时配置**：通过Gradle配置不同版本的行为
- **运行时保护**：动态设置excludeFromRecents（仅针对当前应用）
- **版本区分**：Debug版本方便调试，Release版本安全防护
- **精确控制**：只影响当前应用的任务，不影响其他应用

## 监控和日志

所有关键操作都有详细的日志记录：
- 开机启动过程
- 权限检查结果
- 服务启动状态
- 错误和异常信息

使用XLog.行日志管理，可以通过以下标签过滤：
- `BootReceiver`：开机启动相关日志
- `MainActivity`：主界面相关日志

## 故障排除

### 开机自启动不工作
1. 检查设备所有者权限：`adb shell dpm list-owners`
2. 检查无障碍服务状态
3. 查看系统日志：`adb logcat | grep BootReceiver`
4. 确认应用未被系统电池优化限制

### 最近任务仍然显示
1. 确认是Release版本构建
2. 检查Manifest中的excludeFromRecents配置
3. 重新安装应用（清除数据）

## 兼容性

- **Android版本**：支持Android 8.0+（API 26+）
- **设备兼容性**：支持大部分Android设备
- **厂商定制**：部分厂商可能有额外的自启动限制

## 安全考虑

- 开机自启动仅在具有设备所有者权限时生效
- 隐藏最近任务仅在Release版本中启用
- **精确控制**：只影响当前应用的任务，通过包名验证确保不影响其他应用
- 所有操作都有详细的权限检查和日志记录
- 不会影响系统稳定性和其他应用的正常运行

## 后续优化

- [ ] 支持更多厂商的快速开机广播
- [ ] 添加开机自启动的用户配置选项
- [ ] 优化启动延迟时间的自适应调整
- [ ] 增加更多的安全防护机制
