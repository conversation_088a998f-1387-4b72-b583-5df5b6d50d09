# 自动启动倒计时功能实现

## 功能描述

当BalancePollingManager从服务器获取到大于0的倒计时时间时，系统自动启动CountdownService开始倒计时，无需用户手动操作。

## 实现方案

### 1. 核心逻辑

在BalancePollingManager处理服务器余额响应时，检查计算出的倒计时时间：
- 如果 `tempTimeMillis > 0`，自动启动CountdownService
- 如果 `tempTimeMillis = 0`，表示倒计时结束，不启动服务

### 2. 智能启动策略

为避免重复启动和资源浪费，实现了智能判断机制：

```kotlin
// 检查是否已经有倒计时在运行
val currentRemainingTime = CountdownManager.getRemainingTime()
val isCurrentlyRunning = CountdownManager.isCountdownRunning()

// 如果当前没有倒计时运行，或者新的时间与当前时间差异较大，则启动新的倒计时
val timeDifference = kotlin.math.abs(currentRemainingTime - durationMillis)
val shouldStartNewCountdown = !isCurrentlyRunning || timeDifference > 5000L // 5秒差异阈值
```

**启动条件：**
- 当前没有倒计时运行，OR
- 新时间与当前时间差异超过5秒阈值

## 代码实现

### BalancePollingManager.kt

#### 1. 添加Context支持
```kotlin
// 应用上下文（用于启动服务）
private var applicationContext: Context? = null

/**
 * 初始化余额轮询管理器
 * @param context 应用上下文
 */
fun initialize(context: Context) {
    // 保存应用上下文
    applicationContext = context.applicationContext
    // ... 其他初始化逻辑
}
```

#### 2. 自动启动逻辑
```kotlin
// 同步倒计时时间
calculatedTempTime?.let { tempTimeSeconds ->
    val tempTimeMillis = tempTimeSeconds * 1000L
    
    // 更新CountdownManager中的剩余时间
    CountdownManager.updateRemainingTime(tempTimeMillis)
    
    // 如果倒计时大于0，自动启动倒计时服务
    if (tempTimeMillis > 0) {
        startCountdownService(tempTimeMillis)
    }
}
```

#### 3. 启动服务方法
```kotlin
/**
 * 启动倒计时服务
 * @param durationMillis 倒计时时长（毫秒）
 */
private fun startCountdownService(durationMillis: Long) {
    try {
        val context = applicationContext ?: return
        
        // 智能判断是否需要启动新的倒计时
        val currentRemainingTime = CountdownManager.getRemainingTime()
        val isCurrentlyRunning = CountdownManager.isCountdownRunning()
        val timeDifference = kotlin.math.abs(currentRemainingTime - durationMillis)
        val shouldStartNewCountdown = !isCurrentlyRunning || timeDifference > 5000L
        
        if (shouldStartNewCountdown) {
            val intent = Intent(context, CountdownService::class.java)
            intent.putExtra("duration", durationMillis)
            context.startForegroundService(intent)
            
            XLog.d("倒计时服务启动成功")
        } else {
            XLog.d("倒计时服务已在运行，跳过启动")
        }
        
    } catch (e: Exception) {
        XLog.e("启动倒计时服务失败", e)
    }
}
```

### UserManager.kt

#### 更新初始化调用
```kotlin
// 初始化余额轮询管理器
BalancePollingManager.initialize(appContext)
```

## 工作流程

```mermaid
flowchart TD
    A[用户登录成功] --> B[BalancePollingManager开始轮询]
    B --> C[每60秒查询服务器余额]
    C --> D[服务器返回money字段]
    D --> E[计算倒计时: tempTime = money * 10秒]
    E --> F{tempTime > 0?}
    
    F -->|是| G[检查当前倒计时状态]
    F -->|否| H[更新状态为FINISHED]
    
    G --> I{需要启动新倒计时?}
    I -->|是| J[启动CountdownService]
    I -->|否| K[跳过启动，继续轮询]
    
    J --> L[CountdownService开始倒计时]
    L --> M[更新悬浮窗显示]
    M --> N[倒计时结束触发锁定]
    
    H --> O[悬浮窗显示"锁定"]
    K --> C
    N --> C
    O --> C
```

## 关键特性

### 1. 自动化
- 无需用户手动启动倒计时
- 基于服务器数据自动判断和启动

### 2. 智能化
- 避免重复启动相同的倒计时
- 5秒差异阈值避免频繁重启
- 异常处理确保服务稳定性

### 3. 实时性
- 60秒轮询间隔保证数据及时性
- 立即响应服务器时间变化

### 4. 可靠性
- 完整的异常处理机制
- 日志记录便于问题排查
- 上下文安全检查

## 测试场景

1. **首次登录**：用户登录后，如果服务器返回>0的时间，应自动启动倒计时
2. **时间更新**：轮询过程中时间发生变化，应智能判断是否需要重启倒计时
3. **时间归零**：服务器返回0时间，应停止倒计时并显示锁定状态
4. **网络异常**：网络请求失败时，不应影响现有倒计时运行
5. **服务重启**：应用重启后，应能正确恢复倒计时状态

## 日志输出

关键操作都有详细日志记录：
- `同步倒计时时间: X秒 (X毫秒)`
- `启动倒计时服务: X毫秒`
- `倒计时服务启动成功`
- `倒计时服务已在运行，跳过启动`
- `启动倒计时服务失败`

## 修改文件清单

- `app/src/main/java/com/hwb/timecontroller/business/BalancePollingManager.kt`
  - 添加Context支持
  - 实现自动启动逻辑
  - 添加智能启动策略

- `app/src/main/java/com/hwb/timecontroller/business/UserManager.kt`
  - 更新BalancePollingManager初始化调用

---

**实现日期：** 2025-07-11  
**实现人员：** Augment Agent  
**功能状态：** 已实现，待测试验证
