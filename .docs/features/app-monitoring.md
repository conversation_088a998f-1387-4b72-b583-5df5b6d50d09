# 应用监控功能

## 功能概述
TimeController 的应用监控功能通过无障碍服务实现**应用启动拦截和权限验证**，对受控应用（非白名单应用）进行启动拦截，通过CheckWhenLauncherActivity进行权限验证，实现应用启动控制和付款验证。

## 核心特性

### 🎯 主要功能
- **应用启动拦截**：监听应用启动事件，拦截受控应用启动
- **权限验证流程**：启动CheckWhenLauncherActivity进行透明验证
- **受控应用管理**：基于白名单反向逻辑，不在白名单中的应用即为受控应用
- **付款验证集成**：验证失败时启动LockActivity进行付款验证
- **倒计时状态感知**：倒计时运行期间允许所有应用启动

### 📱 监控范围
- **应用启动事件**：TYPE_WINDOW_STATE_CHANGED
- **窗口内容变化**：TYPE_WINDOW_CONTENT_CHANGED（用于持续监控）
- **受控应用识别**：基于WhitelistManager.isAppInWhitelist()反向判断
- **系统应用过滤**：跳过系统关键应用和本应用组件

## 技术实现

### 服务架构
```kotlin
class AppLifecycleAccessibilityService : AccessibilityService() {
    
    // 核心组件
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    private val handler = Handler(Looper.getMainLooper())
    
    // 状态管理
    private var isDeviceLocked = false
    private var currentForegroundApp: String? = null
    private lateinit var lockStateReceiver: BroadcastReceiver
    
    // 权限助手
    private var overlayPermissionHelper: OverlayPermissionHelper? = null
}
```

### 应用启动拦截机制
```kotlin
override fun onAccessibilityEvent(event: AccessibilityEvent?) {
    if (event == null) return

    // 先让悬浮窗权限助手处理事件
    overlayPermissionHelper?.onAccessibilityEvent(event)

    when (event.eventType) {
        AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
            handleWindowStateChanged(event)
        }
        AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
            handleWindowContentChanged(event)
        }
    }
}
```

## 应用启动拦截逻辑

### 应用启动拦截处理
```kotlin
private fun handleWindowStateChanged(event: AccessibilityEvent) {
    try {
        val packageName = event.packageName?.toString() ?: return
        val className = event.className?.toString() ?: return

        XLog.d("检测到应用启动: $packageName - $className")

        // 检查是否应该拦截应用启动
        if (shouldInterceptAppLaunch(packageName, className)) {
            XLog.d("拦截应用启动: $packageName")
            interceptAppLaunch(packageName, className)
        }

    } catch (e: Exception) {
        XLog.e("处理应用启动拦截时发生错误", e)
    }
}
```

### 拦截判断逻辑
```kotlin
/**
 * 检查是否应该拦截应用启动
 */
private fun shouldInterceptAppLaunch(packageName: String, className: String): Boolean {
    try {
        // 跳过本应用和系统关键应用
        if (packageName == this.packageName || isSystemCriticalApp(packageName)) {
            return false
        }

        // 跳过CheckWhenLauncherActivity和LockActivity
        if (className.contains("CheckWhenLauncherActivity") ||
            className.contains("LockActivity")) {
            return false
        }

        // 检查是否为受控应用（不在白名单中的应用）
        if (!WhitelistManager.isAppInWhitelist(packageName)) {
            // 检查倒计时状态
            if (CountdownManager.isCountdownRunning()) {
                return false // 倒计时期间允许启动
            }
            return true // 需要拦截
        }

        return false
    } catch (e: Exception) {
        XLog.e("检查应用启动拦截时发生错误", e)
        return false
    }
}
```

### 应用启动拦截执行
```kotlin
/**
 * 拦截应用启动
 */
private fun interceptAppLaunch(packageName: String, className: String) {
    try {
        XLog.d("拦截应用启动: $packageName - $className")

        // 获取应用名称
        val appName = try {
            val packageManager = packageManager
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(appInfo).toString()
        } catch (e: Exception) {
            packageName
        }

        // 启动CheckWhenLauncherActivity进行验证
        val intent = Intent(this, CheckWhenLauncherActivity::class.java).apply {
            putExtra(CheckWhenLauncherActivity.EXTRA_TARGET_PACKAGE_NAME, packageName)
            putExtra(CheckWhenLauncherActivity.EXTRA_TARGET_CLASS_NAME, className)
            putExtra(CheckWhenLauncherActivity.EXTRA_TARGET_APP_NAME, appName)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        startActivity(intent)

        XLog.d("已启动验证界面: $packageName")
    } catch (e: Exception) {
        XLog.e("拦截应用启动失败", e)
    }
}
                
                XLog.d("已启动锁定Activity阻止应用: $packageName")
                
            } catch (e: Exception) {
                XLog.e("启动锁定Activity失败", e)
            }
        }, BLOCK_DELAY_MS)
        
    } catch (e: Exception) {
        XLog.e("阻止应用时发生错误", e)
    }
}
```

## 受控应用管理

### 受控应用判断
```kotlin
/**
 * 受控应用定义：不在白名单中的应用即为受控应用
 * 使用WhitelistManager.isAppInWhitelist()进行判断
 */
private fun isControlledApp(packageName: String): Boolean {
    return !WhitelistManager.isAppInWhitelist(packageName)
}

/**
 * 白名单管理使用统一的WhitelistManager
 */
object WhitelistManager {

    /**
     * 检查应用是否在白名单中
     */
    fun isAppInWhitelist(packageName: String): Boolean {
        return try {
            // 使用MMKV存储白名单数据
            val mmkv = MMKV.defaultMMKV()
            val whitelistJson = mmkv.getString("whitelist_apps", "[]") ?: "[]"
            val whitelistApps = parseWhitelistJson(whitelistJson)
            whitelistApps.contains(packageName)
        } catch (e: Exception) {
            XLog.e("检查白名单时发生错误", e)
            false
        }
    }

    /**
     * 添加应用到白名单
     */
    fun addAppToWhitelist(packageName: String) {
        try {
            val mmkv = MMKV.defaultMMKV()
            val whitelistJson = mmkv.getString("whitelist_apps", "[]") ?: "[]"
            val whitelistApps = parseWhitelistJson(whitelistJson).toMutableList()

            if (!whitelistApps.contains(packageName)) {
                whitelistApps.add(packageName)
                val newJson = generateWhitelistJson(whitelistApps)
                mmkv.putString("whitelist_apps", newJson)
                XLog.d("已添加到白名单: $packageName")
            }
        } catch (e: Exception) {
            XLog.e("添加到白名单时发生错误", e)
        }
    }
}
```

### 系统应用识别
```kotlin
private fun isSystemApp(packageName: String): Boolean {
    return try {
        val packageManager = packageManager
        val applicationInfo = packageManager.getApplicationInfo(packageName, 0)
        (applicationInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
    } catch (e: Exception) {
        false
    }
}

private fun isSettingsApp(packageName: String): Boolean {
    val settingsPackages = listOf(
        "com.android.settings",
        "com.android.systemui",
        "com.android.launcher",
        "com.android.launcher3"
    )
    return settingsPackages.contains(packageName)
}
```

## 设备状态监控

### 锁定状态监听
```kotlin
private fun registerLockStateReceiver() {
    lockStateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                Intent.ACTION_SCREEN_OFF -> {
                    isDeviceLocked = true
                    XLog.d("设备已锁定")
                }
                Intent.ACTION_USER_PRESENT -> {
                    isDeviceLocked = false
                    XLog.d("设备已解锁")
                }
                Intent.ACTION_SCREEN_ON -> {
                    // 屏幕亮起，但可能仍在锁屏状态
                    checkDeviceLockStatus()
                }
            }
        }
    }
    
    val filter = IntentFilter().apply {
        addAction(Intent.ACTION_SCREEN_OFF)
        addAction(Intent.ACTION_SCREEN_ON)
        addAction(Intent.ACTION_USER_PRESENT)
    }
    
    registerReceiver(lockStateReceiver, filter)
}
```

### 锁定状态检查
```kotlin
private fun checkDeviceLockStatus() {
    try {
        val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        isDeviceLocked = keyguardManager.isKeyguardLocked
        XLog.d("设备锁定状态: $isDeviceLocked")
    } catch (e: Exception) {
        XLog.e("检查设备锁定状态时发生错误", e)
    }
}
```

## 倒计时状态集成

### 倒计时状态检查
```kotlin
private fun isCountdownActive(): Boolean {
    return try {
        val sharedPreferences = getSharedPreferences("countdown_prefs", Context.MODE_PRIVATE)
        val isActive = sharedPreferences.getBoolean("is_active", false)
        val endTime = sharedPreferences.getLong("end_time", 0)
        val currentTime = System.currentTimeMillis()
        
        isActive && endTime > currentTime
    } catch (e: Exception) {
        XLog.e("检查倒计时状态时发生错误", e)
        false
    }
}
```

### 状态同步
```kotlin
private fun observeCountdownState() {
    // 监听倒计时状态变化
    val sharedPreferences = getSharedPreferences("countdown_prefs", Context.MODE_PRIVATE)
    sharedPreferences.registerOnSharedPreferenceChangeListener { _, key ->
        when (key) {
            "is_active", "end_time" -> {
                val isActive = isCountdownActive()
                XLog.d("倒计时状态变化: $isActive")
                
                if (!isActive) {
                    // 倒计时结束，可以执行相关清理操作
                    onCountdownFinished()
                }
            }
        }
    }
}
```

## 性能优化

### 事件过滤
```kotlin
private fun shouldProcessEvent(event: AccessibilityEvent): Boolean {
    // 过滤不必要的事件
    val packageName = event.packageName?.toString()
    
    // 忽略系统UI事件
    if (packageName == "com.android.systemui") {
        return false
    }
    
    // 忽略重复的包名事件
    if (packageName == currentForegroundApp && 
        event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
        return false
    }
    
    return true
}
```

### 内存管理
```kotlin
override fun onDestroy() {
    super.onDestroy()
    try {
        // 注销广播接收器
        unregisterReceiver(lockStateReceiver)
        
        // 清理权限助手
        overlayPermissionHelper?.cleanup()
        overlayPermissionHelper = null
        
        // 清理其他资源
        handler.removeCallbacksAndMessages(null)
        
        XLog.d("无障碍服务已销毁")
    } catch (e: Exception) {
        XLog.e("销毁无障碍服务时发生错误", e)
    }
}
```

## 配置选项

### 阻止延迟
```kotlin
companion object {
    private const val BLOCK_DELAY_MS = 500L // 阻止非白名单应用的延迟时间
}
```

### 监控范围
- **应用启动监控**：所有用户应用
- **白名单应用**：不受限制
- **系统应用**：不进行阻止
- **设置应用**：允许访问以便用户管理

## 故障排除

### 常见问题
1. **应用监控不生效**
   - 检查无障碍服务权限
   - 确认设备所有者权限
   - 查看服务是否正常运行

2. **白名单应用被误阻止**
   - 检查白名单配置
   - 确认包名是否正确
   - 查看日志输出

3. **系统应用被阻止**
   - 检查系统应用识别逻辑
   - 更新系统应用白名单

### 调试技巧
```kotlin
// 启用详细日志
XLog.d("监控事件: ${event.packageName} - ${event.eventType}")
XLog.d("当前前台应用: $currentForegroundApp")
XLog.d("设备锁定状态: $isDeviceLocked")
XLog.d("倒计时状态: ${isCountdownActive()}")
```
