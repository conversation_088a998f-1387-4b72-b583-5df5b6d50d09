# 互相保活机制

## 概述

本文档描述了TimeController应用的互相保活机制，通过组合模式为各种Service提供强大的保活能力，确保关键服务在系统压力下仍能持续运行。

## 设计原理

### 组合模式设计

由于不同类型的Service需要继承特定的基类，我们采用组合模式而非继承模式：

```kotlin
// 不同Service的基类要求
CountdownService : Service()                    // 普通Service
FloatingWindowService : Service()               // 普通Service  
AccessibilityService : AccessibilityService()  // 必须继承AccessibilityService
```

通过组合模式，任何Service都可以获得保活能力，无论其基类是什么。

## 核心组件

### 1. MutualKeepAliveManager
保活管理器核心类，提供完整的保活功能：

- **心跳机制**：定期发送心跳信号
- **服务监控**：监控伙伴服务状态
- **自动重启**：检测到服务死亡时自动重启
- **故障恢复**：多次重启失败后的降级处理

### 2. ServiceRegistry
全局服务注册表，管理所有参与保活的服务：

- **状态跟踪**：记录每个服务的心跳和状态
- **生命周期管理**：注册/注销服务
- **健康检查**：基于心跳判断服务健康状态

### 3. KeepAliveCapable接口
统一的保活能力接口：

```kotlin
interface KeepAliveCapable {
    fun getKeepAliveManager(): MutualKeepAliveManager
    fun onPartnerServiceDied(serviceClass: Class<out Service>)
    fun onPartnerServiceRestarted(serviceClass: Class<out Service>)
}
```



## 实现方式

### 服务集成示例

```kotlin
class CountdownService : Service(), KeepAliveCapable {
    private lateinit var keepAliveManager: MutualKeepAliveManager
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化保活管理器
        keepAliveManager = MutualKeepAliveManager(
            context = this,
            currentServiceClass = CountdownService::class.java,
            serviceDisplayName = "倒计时服务"
        )
        
        // 注册伙伴服务
        keepAliveManager.registerPartnerServices(
            FloatingWindowService::class.java,
            AppLifecycleManagerService::class.java
        )
        
        // 启动保活
        keepAliveManager.startKeepAlive()
    }
    
    override fun onDestroy() {
        keepAliveManager.onServiceDestroyed()
        super.onDestroy()
    }
    
    override fun getKeepAliveManager() = keepAliveManager
}
```

## 保活策略

### 1. 心跳检测
- **发送频率**：每10秒发送一次心跳
- **超时判断**：30秒内无心跳视为服务死亡
- **轻量级**：心跳信息仅包含时间戳和服务标识

### 2. 服务监控
- **监控频率**：每15秒检查一次伙伴服务状态
- **双重验证**：同时检查系统运行状态和心跳状态
- **延迟启动**：服务启动后延迟2秒开始监控

### 3. 自动重启
- **重启策略**：检测到服务死亡立即重启
- **重试限制**：每个服务最多重启3次
- **重启间隔**：重启后等待2秒让服务稳定

### 4. 故障处理
- **重启计数**：跟踪每个服务的重启次数
- **超限处理**：重启次数超限后停止重启
- **状态重置**：服务正常运行后重置重启计数

## 服务架构

### 当前保活网络

```
AppLifecycleManagerService (生命周期管理)
    ↕ 互相保活
CountdownService (倒计时服务)
    ↕ 互相保活  
FloatingWindowService (悬浮窗服务)
    ↕ 互相保活
AppLifecycleAccessibilityService (无障碍服务)
```

### 保活关系配置

| 服务 | 保活伙伴 | 说明 |
|------|----------|------|
| AppLifecycleManagerService | CountdownService, FloatingWindowService, AccessibilityService | 核心管理服务 |
| CountdownService | FloatingWindowService | 业务服务互保 |
| FloatingWindowService | CountdownService | 业务服务互保 |
| AccessibilityService | CountdownService, FloatingWindowService | 系统服务保护业务服务 |

## 配置参数

### 时间配置
```kotlin
companion object {
    private const val HEARTBEAT_INTERVAL_MS = 10000L      // 心跳间隔10秒
    private const val MONITOR_INTERVAL_MS = 15000L        // 监控间隔15秒
    private const val SERVICE_START_DELAY_MS = 2000L      // 服务启动延迟2秒
    private const val HEARTBEAT_TIMEOUT_MS = 30000L       // 心跳超时30秒
    private const val MAX_RESTART_ATTEMPTS = 3            // 最大重启尝试次数
}
```

### 自定义配置
可以根据具体需求调整：
- **心跳频率**：根据系统性能和电池消耗平衡
- **监控频率**：根据服务重要性调整
- **重启次数**：根据服务稳定性要求调整

## 监控和调试

### 状态查询
```kotlin
// 获取单个服务的保活状态
val status = keepAliveManager.getKeepAliveStatus()

// 获取全局服务注册表状态
val globalStatus = ServiceRegistry.getDetailedStatus()
```

### 日志监控
使用以下标签过滤日志：
- `MutualKeepAliveManager`：保活管理器日志
- `ServiceRegistry`：服务注册表日志
- `[服务显示名]`：具体服务的保活日志

### 调试信息
- **心跳状态**：每个服务的最后心跳时间
- **重启次数**：每个服务的重启尝试次数
- **运行状态**：系统级别的服务运行状态
- **保活关系**：服务间的保活伙伴关系

## 性能考虑

### 资源消耗
- **CPU**：心跳和监控使用协程，CPU消耗极低
- **内存**：每个服务约增加几KB内存占用
- **电池**：定时任务对电池影响微乎其微

### 优化策略
- **智能调度**：使用协程避免阻塞主线程
- **批量操作**：批量检查多个服务状态
- **异常处理**：完善的异常处理避免崩溃

## 扩展性

### 添加新服务
1. 实现`KeepAliveCapable`接口
2. 在`onCreate`中初始化保活管理器
3. 注册需要保活的伙伴服务
4. 在`onDestroy`中清理资源

```kotlin
class YourService : Service(), KeepAliveCapable {
    private lateinit var keepAliveManager: MutualKeepAliveManager

    override fun onCreate() {
        super.onCreate()
        keepAliveManager = MutualKeepAliveManager(this, YourService::class.java, "您的服务")
        keepAliveManager.registerPartnerServices(OtherService::class.java)
        keepAliveManager.startKeepAlive()
    }

    override fun onDestroy() {
        keepAliveManager.onServiceDestroyed()
        super.onDestroy()
    }

    override fun getKeepAliveManager() = keepAliveManager
}
```

### 自定义保活策略
- 继承`MutualKeepAliveManager`实现自定义逻辑
- 重写心跳和监控间隔
- 自定义重启策略和故障处理

## 故障排除

### 常见问题

1. **服务无法重启**
   - 检查权限配置
   - 确认服务在Manifest中正确注册
   - 查看系统是否限制后台服务

2. **心跳丢失**
   - 检查协程是否被取消
   - 确认服务没有被系统杀死
   - 查看是否有异常导致心跳停止

3. **保活失效**
   - 检查电池优化设置
   - 确认应用没有被加入省电白名单
   - 查看系统版本是否有特殊限制

### 调试步骤
1. 查看日志输出确认保活管理器启动
2. 检查服务注册表状态
3. 监控心跳发送和接收
4. 验证服务重启逻辑

## 最佳实践

1. **合理配置伙伴关系**：避免过多的交叉保活
2. **监控资源消耗**：定期检查CPU和内存使用
3. **测试极端情况**：模拟系统压力测试保活效果
4. **日志记录**：保留足够的日志用于问题排查
5. **渐进式部署**：先在少数服务中测试，再全面推广
