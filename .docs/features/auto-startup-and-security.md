# 开机自启动和安全防护功能

## 概述

本文档描述了TimeController应用的开机自启动功能和Release版本的安全防护机制，确保应用在设备重启后能够自动恢复运行，并防止用户意外关闭应用。

## 开机自启动功能

### 功能描述

应用支持在设备开机后自动启动核心服务，包括：
- 自动检查设备所有者权限
- 自动启动无障碍服务（如果已启用）
- 自动启动悬浮窗服务
- 自动恢复应用状态

### 实现原理

#### 1. 权限配置

在`AndroidManifest.xml`中添加了开机启动相关权限：

```xml
<!-- 开机自启动权限 -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

#### 2. 广播接收器

创建了`BootReceiver`类来监听开机广播：

```xml
<!-- 开机启动接收器 -->
<receiver
    android:name=".receiver.BootReceiver"
    android:enabled="true"
    android:exported="true">
    <intent-filter android:priority="1000">
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.QUICKBOOT_POWERON" />
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
        <action android:name="android.intent.action.PACKAGE_REPLACED" />
        <data android:scheme="package" />
        <category android:name="android.intent.category.DEFAULT" />
    </intent-filter>
</receiver>
```

#### 3. 启动逻辑

`BootReceiver`的启动流程：

1. **延迟启动**：开机后延迟3秒启动，确保系统完全启动
2. **权限检查**：检查设备所有者权限
3. **服务启动**：
   - 检查无障碍服务状态
   - 自动授予悬浮窗权限
   - 启动悬浮窗服务

### 支持的启动场景

- **正常开机**：`BOOT_COMPLETED`
- **快速开机**：`QUICKBOOT_POWERON`（部分厂商）
- **应用更新**：`MY_PACKAGE_REPLACED`、`PACKAGE_REPLACED`

## Release版本安全防护

### 功能描述

在Release版本中，应用会自动隐藏在最近任务列表中，防止用户通过最近任务管理器意外关闭应用。

### 实现方式

#### 1. Manifest配置

使用构建变体占位符在不同版本中设置不同的配置：

```kotlin
// build.gradle.kts
buildTypes {
    debug {
        // Debug版本不隐藏最近任务，方便调试
        manifestPlaceholders["excludeFromRecents"] = "false"
    }
    release {
        // Release版本隐藏最近任务
        manifestPlaceholders["excludeFromRecents"] = "true"
    }
}
```

```xml
<!-- AndroidManifest.xml -->
<activity
    android:name=".activity.MainActivity"
    android:exported="true"
    android:excludeFromRecents="${excludeFromRecents}">
```

#### 2. 运行时保护

在`MainActivity`中添加额外的运行时保护：

```kotlin
private fun hideFromRecentTasksInRelease() {
    if (!BuildConfig.DEBUG) {
        // Release版本：动态设置excludeFromRecents（只针对当前应用）
        val activityManager = getSystemService(ACTIVITY_SERVICE) as ActivityManager
        val appTasks = activityManager.appTasks

        // 只处理当前应用的任务
        for (appTask in appTasks) {
            val taskInfo = appTask.taskInfo
            // 确认是当前应用的任务
            if (taskInfo.baseActivity?.packageName == packageName) {
                appTask.setExcludeFromRecents(true)
            }
        }
    }
}
```

### 版本差异

| 版本 | 最近任务显示 | 用途 |
|------|-------------|------|
| Debug | 显示 | 方便开发调试 |
| Release | 隐藏 | 防止用户误操作 |

## 安全考虑

### 权限要求

开机自启动功能需要以下条件：
1. **设备所有者权限**：必须具有Device Owner权限
2. **无障碍服务**：需要用户手动启用无障碍服务
3. **系统权限**：需要RECEIVE_BOOT_COMPLETED权限

### 启动保护

1. **权限验证**：启动前验证必要权限
2. **异常处理**：完善的异常处理机制
3. **日志记录**：详细的启动日志记录

### 用户体验

1. **透明启动**：后台静默启动，不干扰用户
2. **状态恢复**：自动恢复应用之前的状态
3. **服务连续性**：确保核心服务持续运行

## 故障排除

### 开机自启动失败

1. **检查权限**：确认设备所有者权限
2. **检查服务**：确认无障碍服务状态
3. **查看日志**：检查BootReceiver的日志输出

### 最近任务仍然显示

1. **检查构建类型**：确认是Release版本
2. **检查Manifest**：确认excludeFromRecents配置
3. **重新安装**：清除数据后重新安装

## 相关文件

- `app/src/main/java/com/hwb/timecontroller/receiver/BootReceiver.kt`
- `app/src/main/java/com/hwb/timecontroller/activity/MainActivity.kt`
- `app/src/main/AndroidManifest.xml`
- `app/build.gradle.kts`

## 注意事项

1. **设备兼容性**：部分厂商可能有自定义的开机广播
2. **权限限制**：Android 8.0+对后台服务有限制
3. **用户设置**：用户可能在系统设置中禁用自启动
4. **电池优化**：需要将应用加入电池优化白名单
