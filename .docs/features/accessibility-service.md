# 无障碍服务功能

## 功能概述
TimeController 的无障碍服务是应用的核心组件，负责**应用启动拦截**、权限自动获取、设备状态管理等多项功能。通过监听应用启动事件，实现对受控应用的启动拦截和权限验证。

## 核心特性

### 🎯 主要功能
- **应用启动拦截**：监听应用启动事件，拦截受控应用启动
- **权限验证集成**：启动CheckWhenLauncherActivity进行透明验证
- **自动权限获取**：自动获取悬浮窗权限
- **设备状态监控**：监控设备锁定/解锁状态
- **受控应用管理**：基于白名单反向逻辑管理受控应用
- **保活机制集成**：与其他服务互相保活

### 📱 服务组件
- **AppLifecycleAccessibilityService**：主要的无障碍服务，集成应用启动拦截
- **OverlayPermissionHelper**：悬浮窗权限获取助手（精简版）
- **AccessibilityOverlayPermissionHelper**：悬浮窗权限获取助手（完整版）
- **MutualKeepAliveManager**：保活管理器
- **设备策略管理器**：管理设备所有者权限

## 技术实现

### 服务架构
```kotlin
class AppLifecycleAccessibilityService : AccessibilityService() {
    
    // 核心组件
    private lateinit var devicePolicyManager: DevicePolicyManager
    private lateinit var adminComponent: ComponentName
    private val handler = Handler(Looper.getMainLooper())
    
    // 状态管理
    private var isDeviceLocked = false
    private var currentForegroundApp: String? = null
    private lateinit var lockStateReceiver: BroadcastReceiver
    
    // 权限助手
    private var overlayPermissionHelper: OverlayPermissionHelper? = null
}
```

### 服务连接处理
```kotlin
override fun onServiceConnected() {
    super.onServiceConnected()
    XLog.d("无障碍服务已连接")

    // 初始化设备策略管理器
    devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    adminComponent = ComponentName(this, AppDeviceAdminReceiver::class.java)

    // 初始化悬浮窗权限助手
    overlayPermissionHelper = OverlayPermissionHelper(this)

    // 检查设备锁定状态
    checkDeviceLockStatus()

    // 注册锁定状态广播接收器
    registerLockStateReceiver()

    // 自动获取悬浮窗权限并启动悬浮窗
    autoSetupFloatingWindow()

    Toast.makeText(this, "应用监控服务已启动", Toast.LENGTH_SHORT).show()
}
```

## 事件处理

### 无障碍事件监听
```kotlin
override fun onAccessibilityEvent(event: AccessibilityEvent?) {
    if (event == null) return

    // 先让悬浮窗权限助手处理事件
    overlayPermissionHelper?.onAccessibilityEvent(event)

    when (event.eventType) {
        AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED -> {
            handleWindowStateChanged(event)
        }
        AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED -> {
            handleWindowContentChanged(event)
        }
    }
}
```

### 窗口状态变化处理
```kotlin
private fun handleWindowStateChanged(event: AccessibilityEvent) {
    try {
        val packageName = event.packageName?.toString()
        val className = event.className?.toString()
        
        if (packageName.isNullOrEmpty()) return
        
        XLog.d("窗口状态变化: $packageName/$className")
        
        // 更新当前前台应用
        currentForegroundApp = packageName
        
        // 检查是否需要阻止应用
        if (shouldBlockApp(packageName)) {
            blockApp(packageName)
        }
        
    } catch (e: Exception) {
        XLog.e("处理窗口状态变化时发生错误", e)
    }
}
```

## 权限管理

### 自动悬浮窗权限获取
```kotlin
private fun autoSetupFloatingWindow() {
    try {
        // 检查是否有设备所有者权限
        if (!devicePolicyManager.isDeviceOwnerApp(packageName)) {
            XLog.w("没有设备所有者权限，跳过悬浮窗设置")
            return
        }

        overlayPermissionHelper?.autoGrantOverlayPermission(
            onGranted = {
                XLog.d("悬浮窗权限获取成功，启动悬浮窗服务")
                try {
                    FloatingWindowService.start(this@AppLifecycleAccessibilityService)
                    Toast.makeText(this, "悬浮窗已启动", Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    XLog.e("启动悬浮窗服务失败", e)
                }
            },
            onFailed = {
                XLog.w("悬浮窗权限获取失败")
                Toast.makeText(this, "悬浮窗权限获取失败", Toast.LENGTH_SHORT).show()
            }
        )
    } catch (e: Exception) {
        XLog.e("自动设置悬浮窗失败", e)
    }
}
```

### 设备所有者权限检查
```kotlin
private fun isDeviceOwner(): Boolean {
    return try {
        devicePolicyManager.isDeviceOwnerApp(packageName)
    } catch (e: Exception) {
        XLog.e("检查设备所有者权限失败", e)
        false
    }
}
```

## 应用启动拦截

### 应用启动拦截集成
```kotlin
/**
 * 检查是否应该拦截应用启动
 */
private fun shouldInterceptAppLaunch(packageName: String, className: String): Boolean {
    try {
        // 跳过本应用和系统关键应用
        if (packageName == this.packageName || isSystemCriticalApp(packageName)) {
            return false
        }

        // 跳过CheckWhenLauncherActivity和LockActivity
        if (className.contains("CheckWhenLauncherActivity") ||
            className.contains("LockActivity")) {
            return false
        }

        // 检查是否为受控应用且需要拦截
        if (!WhitelistManager.isAppInWhitelist(packageName)) {
            // 检查倒计时状态
            if (CountdownManager.isCountdownRunning()) {
                return false // 倒计时期间允许启动
            }
            return true // 需要拦截
        }

        return false
    } catch (e: Exception) {
        XLog.e("检查应用启动拦截时发生错误", e)
        return false
    }
}
```

### 应用启动拦截执行
```kotlin
/**
 * 拦截应用启动
 */
private fun interceptAppLaunch(packageName: String, className: String) {
    try {
        XLog.d("拦截应用启动: $packageName - $className")

        // 获取应用名称
        val appName = try {
            val packageManager = packageManager
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            packageManager.getApplicationLabel(appInfo).toString()
        } catch (e: Exception) {
            packageName
        }

        // 启动CheckWhenLauncherActivity进行验证
        val intent = Intent(this, CheckWhenLauncherActivity::class.java).apply {
            putExtra(CheckWhenLauncherActivity.EXTRA_TARGET_PACKAGE_NAME, packageName)
            putExtra(CheckWhenLauncherActivity.EXTRA_TARGET_CLASS_NAME, className)
            putExtra(CheckWhenLauncherActivity.EXTRA_TARGET_APP_NAME, appName)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        startActivity(intent)

        XLog.d("已启动验证界面: $packageName")
    } catch (e: Exception) {
        XLog.e("拦截应用启动失败", e)
    }
}
        handler.postDelayed({
            try {
                // 启动锁定Activity
                val intent = Intent(this, LockActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                    putExtra("blocked_app", packageName)
                }
                startActivity(intent)
                
                XLog.d("已阻止应用: $packageName")
            } catch (e: Exception) {
                XLog.e("阻止应用时发生错误", e)
            }
        }, BLOCK_DELAY_MS)
        
    } catch (e: Exception) {
        XLog.e("阻止应用操作失败", e)
    }
}
```

## 设备状态监控

### 锁定状态检查
```kotlin
private fun checkDeviceLockStatus() {
    try {
        val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
        isDeviceLocked = keyguardManager.isKeyguardLocked
        XLog.d("设备锁定状态: $isDeviceLocked")
    } catch (e: Exception) {
        XLog.e("检查设备锁定状态失败", e)
        isDeviceLocked = false
    }
}
```

### 广播接收器注册
```kotlin
private fun registerLockStateReceiver() {
    try {
        lockStateReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_SCREEN_OFF -> {
                        isDeviceLocked = true
                        XLog.d("设备已锁定")
                    }
                    Intent.ACTION_USER_PRESENT -> {
                        isDeviceLocked = false
                        XLog.d("设备已解锁")
                    }
                }
            }
        }
        
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_USER_PRESENT)
        }
        
        registerReceiver(lockStateReceiver, filter)
        XLog.d("锁定状态广播接收器已注册")
        
    } catch (e: Exception) {
        XLog.e("注册锁定状态广播接收器失败", e)
    }
}
```

## 配置管理

### 服务配置
```xml
<!-- accessibility_service_config.xml -->
<accessibility-service xmlns:android="http://schemas.android.com/apk/res/android"
    android:accessibilityEventTypes="typeWindowStateChanged|typeWindowContentChanged"
    android:accessibilityFeedbackType="feedbackGeneric"
    android:accessibilityFlags="flagDefault"
    android:canRetrieveWindowContent="true"
    android:notificationTimeout="100"
    android:packageNames="com.android.settings" />
```

### 权限声明
```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE"
    tools:ignore="ProtectedPermissions" />
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS"
    tools:ignore="ProtectedPermissions" />
```

## 生命周期管理

### 服务启动
```kotlin
// 自动启用无障碍服务
fun autoEnableAccessibilityService(
    onStatusUpdate: (String) -> Unit,
    onServiceEnabled: () -> Unit,
    onServiceFailed: () -> Unit
) {
    if (!isDeviceOwner()) {
        onStatusUpdate("需要设备所有者权限才能自动启用无障碍服务")
        onServiceFailed()
        return
    }

    if (isAccessibilityServiceEnabled()) {
        onStatusUpdate("无障碍服务已启用")
        onServiceEnabled()
        return
    }

    // 启用服务逻辑...
}
```

### 服务销毁
```kotlin
override fun onDestroy() {
    super.onDestroy()
    try {
        unregisterReceiver(lockStateReceiver)
    } catch (e: Exception) {
        XLog.e("注销广播接收器失败", e)
    }

    // 清理悬浮窗权限助手
    overlayPermissionHelper?.cleanup()
    overlayPermissionHelper = null

    XLog.d("无障碍服务已销毁")
}
```

## 性能优化

### 事件过滤
- 只监听必要的事件类型
- 过滤无关的包名和类名
- 使用延迟处理避免频繁操作

### 内存管理
- 及时清理不用的资源
- 避免内存泄漏
- 合理使用缓存

### 电池优化
- 减少不必要的后台操作
- 优化事件处理频率
- 使用高效的数据结构

## 故障排除

### 常见问题
1. **服务无法启动**
   - 检查无障碍权限
   - 确认设备所有者权限
   - 查看系统设置中的无障碍服务列表

2. **应用监控不生效**
   - 检查事件监听配置
   - 确认白名单设置
   - 验证包名匹配逻辑

3. **权限获取失败**
   - 检查设备所有者权限
   - 确认目标设置页面结构
   - 查看详细错误日志

### 调试技巧
```kotlin
// 启用详细日志
XLog.d("无障碍服务状态: ${isServiceEnabled}")
XLog.d("当前前台应用: $currentForegroundApp")
XLog.d("设备锁定状态: $isDeviceLocked")
XLog.d("权限助手状态: ${overlayPermissionHelper != null}")
```
