# 悬浮窗功能

## 功能概述
TimeController 的悬浮窗功能提供了一个全局可见的控制界面，用户可以在任何应用中查看倒计时状态并进行快捷操作。悬浮窗与应用启动控制系统集成，倒计时运行期间允许所有应用正常启动。

## 核心特性

### 🎯 主要功能
- **倒计时显示**：实时显示剩余时间
- **快捷操作**：提供回到主页和设置的快捷按钮
- **拖拽移动**：支持拖拽调整悬浮窗位置
- **自动权限获取**：通过无障碍服务自动获取悬浮窗权限
- **智能启动**：根据权限状态智能决定启动策略

### 📱 界面组件
- **倒计时文本**：显示格式化的剩余时间
- **主页按钮**：快速返回桌面
- **设置按钮**：打开应用主界面

## 技术实现

### 服务架构
```kotlin
class FloatingWindowService : Service() {
    
    companion object {
        fun start(context: Context) {
            val intent = Intent(context, FloatingWindowService::class.java)
            context.startService(intent)
        }
        
        fun stop(context: Context) {
            val intent = Intent(context, FloatingWindowService::class.java)
            context.stopService(intent)
        }
    }
    
    // 核心组件
    private var windowManager: WindowManager? = null
    private var floatingView: View? = null
    private var layoutParams: WindowManager.LayoutParams? = null
}
```

### 权限管理
```kotlin
// 权限检查
private fun checkOverlayPermission(): Boolean {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        Settings.canDrawOverlays(this)
    } else {
        true
    }
}

// 设备所有者权限检查
private fun isDeviceOwner(): Boolean {
    return try {
        val devicePolicyManager = getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
        devicePolicyManager.isDeviceOwnerApp(packageName)
    } catch (e: Exception) {
        false
    }
}
```

### 布局参数设置
```kotlin
private fun setupLayoutParams() {
    val type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
    
    layoutParams = WindowManager.LayoutParams(
        WindowManager.LayoutParams.WRAP_CONTENT,
        WindowManager.LayoutParams.WRAP_CONTENT,
        type,
        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
        PixelFormat.TRANSLUCENT
    ).apply {
        gravity = Gravity.TOP or Gravity.START
        x = 100
        y = 100
    }
}
```

## 启动策略

### 智能启动流程
```
应用启动
    ↓
检查设备所有者权限
    ↓
检查悬浮窗权限状态
    ↓
┌─────────────────────────────────────┐
│ 情况1: 已有悬浮窗权限                │
│ → 直接启动悬浮窗服务                │
├─────────────────────────────────────┤
│ 情况2: 有无障碍权限但没有悬浮窗权限  │
│ → 通过无障碍服务自动获取权限后启动   │
├─────────────────────────────────────┤
│ 情况3: 都没有权限                   │
│ → 通过MainActivity引导用户手动启用   │
└─────────────────────────────────────┘
```

### 自动权限获取
```kotlin
// 在无障碍服务中自动获取悬浮窗权限
overlayPermissionHelper?.autoGrantOverlayPermission(
    onGranted = {
        XLog.d("悬浮窗权限获取成功，启动悬浮窗服务")
        FloatingWindowService.start(this@AppLifecycleAccessibilityService)
        Toast.makeText(this, "悬浮窗已启动", Toast.LENGTH_SHORT).show()
    },
    onFailed = {
        XLog.w("悬浮窗权限获取失败")
        Toast.makeText(this, "悬浮窗权限获取失败", Toast.LENGTH_SHORT).show()
    }
)
```

## 用户交互

### 触摸事件处理
```kotlin
private fun setupTouchListener() {
    floatingView?.setOnTouchListener(object : View.OnTouchListener {
        private var initialX = 0
        private var initialY = 0
        private var initialTouchX = 0f
        private var initialTouchY = 0f
        
        override fun onTouch(v: View, event: MotionEvent): Boolean {
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 记录初始位置
                    initialX = layoutParams?.x ?: 0
                    initialY = layoutParams?.y ?: 0
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    return true
                }
                MotionEvent.ACTION_MOVE -> {
                    // 更新悬浮窗位置
                    layoutParams?.x = initialX + (event.rawX - initialTouchX).toInt()
                    layoutParams?.y = initialY + (event.rawY - initialTouchY).toInt()
                    windowManager?.updateViewLayout(floatingView, layoutParams)
                    return true
                }
            }
            return false
        }
    })
}
```

### 按钮功能
```kotlin
private fun initViews() {
    floatingView?.let { view ->
        tvCountdown = view.findViewById(R.id.tv_countdown)
        btnHome = view.findViewById(R.id.btn_home)
        btnMainActivity = view.findViewById(R.id.btn_main_activity)
        
        // 主页按钮
        btnHome?.setOnClickListener {
            openHomeApp()
        }
        
        // 设置按钮
        btnMainActivity?.setOnClickListener {
            openMainActivity()
        }
    }
}
```

## 状态监控

### 倒计时状态观察
```kotlin
private fun observeCountdownState() {
    countdownObserverJob = serviceScope.launch {
        while (isActive) {
            try {
                val sharedPreferences = getSharedPreferences("countdown_prefs", Context.MODE_PRIVATE)
                val endTime = sharedPreferences.getLong("end_time", 0)
                val isActive = sharedPreferences.getBoolean("is_active", false)
                
                if (isActive && endTime > 0) {
                    val currentTime = System.currentTimeMillis()
                    val remainingTime = endTime - currentTime
                    
                    if (remainingTime > 0) {
                        updateCountdownDisplay(remainingTime)
                    } else {
                        updateCountdownDisplay(0)
                    }
                } else {
                    updateCountdownDisplay(0)
                }
                
                delay(1000) // 每秒更新一次
            } catch (e: Exception) {
                XLog.e("观察倒计时状态时发生错误", e)
                delay(1000)
            }
        }
    }
}
```

### 显示更新
```kotlin
private fun updateCountdownDisplay(remainingTimeMs: Long) {
    try {
        val formattedTime = if (remainingTimeMs > 0) {
            formatTime(remainingTimeMs)
        } else {
            "00:00:00"
        }
        
        // 在主线程更新UI
        Handler(Looper.getMainLooper()).post {
            tvCountdown?.text = formattedTime
        }
    } catch (e: Exception) {
        XLog.e("更新倒计时显示时发生错误", e)
    }
}

private fun formatTime(timeMs: Long): String {
    val totalSeconds = timeMs / 1000
    val hours = totalSeconds / 3600
    val minutes = (totalSeconds % 3600) / 60
    val seconds = totalSeconds % 60
    return String.format("%02d:%02d:%02d", hours, minutes, seconds)
}
```

## 生命周期管理

### 服务生命周期
```kotlin
override fun onCreate() {
    super.onCreate()
    XLog.d("FloatingWindowService onCreate")
    
    // 权限检查
    if (!isDeviceOwner()) {
        XLog.w("没有设备所有者权限，停止悬浮窗服务")
        stopSelf()
        return
    }
    
    if (checkOverlayPermission()) {
        createFloatingWindow()
        observeCountdownState()
    } else {
        XLog.w("没有悬浮窗权限")
        stopSelf()
    }
}

override fun onDestroy() {
    super.onDestroy()
    XLog.d("FloatingWindowService onDestroy")
    
    // 清理资源
    removeFloatingWindow()
    countdownObserverJob?.cancel()
    serviceScope.cancel()
}
```

## 配置选项

### 布局配置
- **位置**：默认在屏幕左上角 (100, 100)
- **大小**：自适应内容大小
- **透明度**：半透明背景
- **层级**：TYPE_APPLICATION_OVERLAY

### 更新频率
- **倒计时更新**：每秒一次
- **状态检查**：实时响应

## 兼容性

### Android版本支持
- **Android 6.0+**：完全支持
- **Android 8.0+**：使用 TYPE_APPLICATION_OVERLAY
- **Android 10+**：适配新的权限模型

### 设备适配
- **原生Android**：完全支持
- **各厂商定制系统**：通过关键词适配

## 故障排除

### 常见问题
1. **悬浮窗不显示**
   - 检查悬浮窗权限
   - 确认设备所有者权限
   - 查看服务是否正常启动

2. **拖拽不响应**
   - 检查触摸事件处理
   - 确认布局参数设置

3. **倒计时不更新**
   - 检查SharedPreferences数据
   - 确认协程是否正常运行

### 调试技巧
```kotlin
// 启用详细日志
XLog.d("悬浮窗服务状态: ${service.isRunning}")
XLog.d("权限状态: overlay=${hasOverlay}, deviceOwner=${isDeviceOwner}")
XLog.d("倒计时状态: active=${isActive}, remaining=${remainingTime}")
```
