# TimeController 完整工作流程图

本文档包含TimeController应用的完整工作流程Mermaid图表，展示各个模块之间的交互关系、数据流向和决策分支。

## 1. 应用启动和权限初始化流程

```mermaid
flowchart TD
    A[应用启动] --> B{检查权限状态}
    B --> C{设备所有者权限?}
    C -->|是| D{无障碍权限?}
    C -->|否| E[启动MainActivity<br/>引导用户获取权限]
    
    D -->|是| F{悬浮窗权限?}
    D -->|否| G[场景2: 启动MainActivity<br/>引导获取无障碍权限]
    
    F -->|是| H[场景1: 完整权限<br/>直接启动悬浮窗服务]
    F -->|否| I[场景3: 自动获取悬浮窗权限<br/>通过无障碍服务]
    
    E --> J[用户手动配置权限]
    G --> J
    I --> K{权限获取成功?}
    K -->|是| H
    K -->|否| G
    
    H --> L[启动所有核心服务]
    L --> M[AppLifecycleManagerService]
    L --> N[FloatingWindowService]
    L --> O[AppLifecycleAccessibilityService]
    
    M --> P[互相保活机制启动]
    N --> P
    O --> P
    
    P --> Q[系统就绪状态]
```

## 2. 应用启动拦截和验证流程

```mermaid
flowchart TD
    A[用户点击应用图标] --> B[系统启动应用]
    B --> C[无障碍服务监听<br/>TYPE_WINDOW_STATE_CHANGED]
    C --> D{是否为受控应用?<br/>!WhitelistManager.isAppInWhitelist}
    
    D -->|否| E[白名单应用<br/>允许正常启动]
    D -->|是| F[受控应用<br/>拦截启动]
    
    F --> G[启动CheckWhenLauncherActivity<br/>透明验证界面]
    G --> H[CheckWhenLauncherViewModel<br/>开始验证流程]
    
    H --> I{检查倒计时状态<br/>CountdownManager.isCountdownRunning}
    I -->|运行中| J[ALLOW_LAUNCH<br/>允许启动]
    I -->|未运行| K[执行权限验证逻辑]
    
    K --> L{验证结果判断}
    L -->|ALLOW_LAUNCH| M[启动目标应用]
    L -->|REQUIRE_PAYMENT| N[启动LockActivity<br/>登录验证界面]
    L -->|BLOCKED_BY_COUNTDOWN| O[阻止启动<br/>显示倒计时信息]
    L -->|BLOCKED_BY_LIMIT| P[启动LockActivity<br/>次数限制验证]
    
    J --> M
    M --> Q[应用正常运行]
    N --> R[登录验证流程]
    P --> R
    O --> S[返回桌面或前一应用]
```

## 3. 登录验证和付款流程

```mermaid
flowchart TD
    A[LockActivity启动] --> B[LoginViewModel初始化]
    B --> C[startLoginFlow开始登录]
    C --> D[HTTP请求获取二维码<br/>POST /api/payment/qrcode]
    
    D --> E{HTTP请求成功?}
    E -->|否| F[显示错误信息<br/>LoginState.ERROR]
    E -->|是| G[解析QRCodeResponse<br/>获取orderId和base64]
    
    G --> H[Coil加载显示二维码<br/>LoginState.WAITING_LOGIN]
    H --> I[GlobalWebSocketManager<br/>建立WebSocket连接]
    
    I --> J{WebSocket连接成功?}
    J -->|否| K[仅HTTP模式<br/>显示连接警告]
    J -->|是| L[监听登录状态消息<br/>messageFlow.collect]
    
    L --> M{收到WebSocket消息}
    M -->|login_status| N[handleLoginStatusUpdate]
    M -->|heartbeat| O[心跳保持连接]
    M -->|error| P[显示错误信息]
    
    N --> Q{登录状态判断}
    Q -->|PENDING| R[等待用户扫码]
    Q -->|SUCCESS| S[LoginState.SUCCESS<br/>登录成功]
    Q -->|FAILED| T[LoginState.FAILED<br/>登录失败]
    Q -->|EXPIRED| U[LoginState.EXPIRED<br/>二维码过期]
    
    S --> V[启动目标应用<br/>launchTargetApp]
    T --> W[显示重试选项]
    U --> X[提示刷新二维码]
    
    V --> Y[应用启动成功<br/>finish LockActivity]
    W --> Z[用户选择重试或取消]
    X --> AA[refreshQRCode重新获取]
```

## 4. 倒计时服务和设备锁定流程

```mermaid
flowchart TD
    A[MainActivity用户设置倒计时] --> B[startCountdown启动倒计时]
    B --> C[启动CountdownService前台服务]
    C --> D[CountdownManager.startCountdown<br/>更新状态为RUNNING]

    D --> E[创建CountDownTimer<br/>每秒更新一次]
    E --> F[onTick更新剩余时间]
    F --> G[CountdownManager.updateRemainingTime<br/>更新StateFlow]
    G --> H[更新通知显示剩余时间]

    H --> I{倒计时是否结束?}
    I -->|否| F
    I -->|是| J[onFinish倒计时结束]

    J --> K[CountdownManager.finishCountdown<br/>状态变为FINISHED]
    K --> L[CountdownManager.setDeviceLocked(true)<br/>设备锁定状态]
    L --> M[启动LockActivity锁定界面]

    M --> N[LockActivity进入LockTask模式<br/>全屏锁定]
    N --> O[监听解锁广播<br/>ACTION_UNLOCK_DEVICE]

    O --> P{收到解锁指令?}
    P -->|否| Q[保持锁定状态]
    P -->|是| R[unlockDevice解锁设备]

    R --> S[CountdownManager.cancelCountdown<br/>取消倒计时]
    S --> T[stopLockTask退出锁定模式]
    T --> U[finish关闭LockActivity]

    Q --> P
```

## 5. 悬浮窗服务和应用监控流程

```mermaid
flowchart TD
    A[FloatingWindowService启动] --> B{检查设备所有者权限}
    B -->|否| C[停止服务<br/>权限不足]
    B -->|是| D[创建悬浮窗布局<br/>layout_floating_window]

    D --> E[WindowManager.addView<br/>显示悬浮窗]
    E --> F[注册AppLifecycleListener<br/>监听应用切换]

    F --> G[监听CountdownManager状态<br/>countdownData.collect]
    G --> H{倒计时状态变化}

    H -->|RUNNING| I[更新悬浮窗显示剩余时间<br/>tvCountdown.text]
    H -->|FINISHED| J[显示设备已锁定<br/>tvCountdown.text = "已锁定"]
    H -->|CANCELLED| K[显示倒计时已取消<br/>tvCountdown.text = "已解锁"]
    H -->|IDLE| L[显示空闲状态<br/>tvCountdown.text = "未启动"]

    I --> M[悬浮窗UI更新完成]
    J --> M
    K --> M
    L --> M

    M --> N{用户交互}
    N -->|点击悬浮窗| O[显示操作菜单<br/>启动MainActivity等]
    N -->|拖拽悬浮窗| P[更新悬浮窗位置<br/>layoutParams更新]
    N -->|无操作| Q[继续监听状态变化]

    O --> R[执行相应操作]
    P --> S[保存新位置]
    Q --> G

    R --> G
    S --> G
```

## 6. 白名单管理和应用分类流程

```mermaid
flowchart TD
    A[WhitelistManager初始化] --> B[加载默认系统白名单<br/>DEFAULT_SYSTEM_WHITELIST]
    B --> C[从MMKV加载用户白名单<br/>KEY_WHITELIST]
    C --> D[合并系统和用户白名单<br/>getWhitelistPackages]

    D --> E[同步更新LockTask白名单<br/>updateLockTaskPackages]
    E --> F[DevicePolicyManager.setLockTaskPackages]

    F --> G[白名单初始化完成]

    G --> H{应用启动检查}
    H --> I[isAppInWhitelist检查包名]
    I --> J{包名在白名单中?}

    J -->|是| K[白名单应用<br/>允许正常启动]
    J -->|否| L[受控应用<br/>需要验证]

    K --> M[正常应用流程]
    L --> N[启动拦截流程]

    O[用户添加应用到白名单] --> P[addToWhitelist]
    P --> Q[更新用户白名单到MMKV]
    Q --> R[同步更新LockTask白名单]
    R --> S[白名单更新完成]

    T[用户从白名单移除应用] --> U[removeFromWhitelist]
    U --> V{是否为系统默认白名单?}
    V -->|是| W[拒绝移除<br/>系统应用保护]
    V -->|否| X[从用户白名单移除]
    X --> Y[更新MMKV存储]
    Y --> Z[同步更新LockTask白名单]
    Z --> AA[移除完成]
```

## 7. 互相保活机制流程

```mermaid
flowchart TD
    A[服务启动] --> B[实现KeepAliveCapable接口]
    B --> C[创建MutualKeepAliveManager实例]
    C --> D[registerPartnerServices<br/>注册伙伴服务]

    D --> E[startKeepAlive启动保活]
    E --> F[ServiceRegistry.registerService<br/>注册到全局服务表]

    F --> G[启动定时检查任务<br/>每30秒检查一次]
    G --> H[checkPartnerServices检查伙伴状态]

    H --> I{伙伴服务是否运行?}
    I -->|是| J[服务正常<br/>继续监控]
    I -->|否| K[检测到服务停止]

    K --> L[attemptRestart尝试重启服务]
    L --> M{重启成功?}
    M -->|是| N[记录重启成功<br/>重置重启计数]
    M -->|否| O[增加重启计数<br/>等待下次尝试]

    N --> P[发送保活广播<br/>通知其他服务]
    O --> Q{达到最大重启次数?}
    Q -->|是| R[停止重启尝试<br/>记录失败日志]
    Q -->|否| S[等待重启间隔<br/>继续尝试]

    J --> T[30秒后再次检查]
    P --> T
    S --> T
    T --> H

    U[服务被系统杀死] --> V[其他服务检测到异常]
    V --> L
```

## 8. WebSocket连接和消息处理流程

```mermaid
flowchart TD
    A[GlobalWebSocketManager.connect] --> B{当前是否已连接?}
    B -->|是| C[忽略重复连接请求]
    B -->|否| D[开始连接流程]

    D --> E[设置连接状态为CONNECTING<br/>_connectionStateFlow.emit]
    E --> F[创建WebSocket连接<br/>webSocketClient.webSocket]

    F --> G{连接成功?}
    G -->|否| H[handleConnectionError<br/>连接失败处理]
    G -->|是| I[设置连接状态为CONNECTED<br/>isConnected = true]

    I --> J[启动心跳机制<br/>startHeartbeat]
    J --> K[监听消息循环<br/>for frame in incoming]

    K --> L{收到消息类型}
    L -->|Frame.Text| M[handleIncomingMessage<br/>处理文本消息]
    L -->|Frame.Close| N[handleConnectionClosed<br/>连接关闭处理]
    L -->|其他| O[忽略其他类型帧]

    M --> P[Json.decodeFromString<br/>解析WebSocket消息]
    P --> Q[_messageFlow.emit<br/>分发消息给订阅者]

    Q --> R{消息类型判断}
    R -->|login_status| S[LoginViewModel处理登录状态]
    R -->|heartbeat| T[心跳响应处理]
    R -->|error| U[错误消息处理]

    N --> V[设置连接状态为DISCONNECTED<br/>isConnected = false]
    V --> W{是否需要重连?}
    W -->|是| X[scheduleReconnect<br/>安排重连任务]
    W -->|否| Y[停止WebSocket服务]

    X --> Z[等待重连间隔<br/>WS_RECONNECT_DELAY_MS]
    Z --> AA{重连次数检查}
    AA -->|未超限| D
    AA -->|超限| BB[停止重连尝试]

    H --> CC{是否应该重连?}
    CC -->|是| X
    CC -->|否| Y
```

## 9. 完整系统交互概览

```mermaid
flowchart TB
    subgraph "用户界面层"
        MA[MainActivity<br/>主界面]
        LA[LockActivity<br/>登录/锁定界面]
        CS[CheckService<br/>AIDL验证服务]
        WA[WhitelistActivity<br/>白名单管理]
        FW[FloatingWindow<br/>悬浮窗]
    end

    subgraph "业务逻辑层"
        CSL[CheckService Logic<br/>启动验证逻辑]
        LVM[LoginViewModel<br/>登录验证逻辑]
        CM[CountdownManager<br/>倒计时状态管理]
        WM[WhitelistManager<br/>白名单管理]
        ALM[AppLifecycleManager<br/>应用生命周期管理]
    end

    subgraph "服务层"
        AAS[AppLifecycleAccessibilityService<br/>无障碍服务]
        CS[CountdownService<br/>倒计时服务]
        FWS[FloatingWindowService<br/>悬浮窗服务]
        ALMS[AppLifecycleManagerService<br/>生命周期管理服务]
    end

    subgraph "网络层"
        GWM[GlobalWebSocketManager<br/>WebSocket管理]
        NM[NetworkManager<br/>HTTP客户端]
        LAM[LoginApiManager<br/>登录API]
    end

    subgraph "系统层"
        AF[Android Framework]
        DOA[Device Owner API]
        AA[Accessibility API]
        WMA[WindowManager API]
    end

    %% 主要数据流
    MA --> CS
    CS --> CM
    CM --> FWS
    FWS --> FW

    AAS --> CWL
    CWL --> CWLVM
    CWLVM --> WM
    CWLVM --> CM

    CWL --> LA
    LA --> LVM
    LVM --> LAM
    LAM --> NM
    LVM --> GWM

    %% 服务间保活
    AAS <--> CS
    CS <--> FWS
    FWS <--> ALMS
    ALMS <--> AAS

    %% 系统API调用
    AAS --> AA
    CS --> DOA
    FWS --> WMA
    WM --> DOA
    NM --> AF
```

## 总结

以上流程图展示了TimeController应用的完整工作流程，包括：

1. **应用启动和权限初始化**：智能权限检查和服务启动策略
2. **应用启动拦截和验证**：基于无障碍服务的应用启动控制
3. **登录验证和付款**：HTTP+WebSocket的登录验证机制
4. **倒计时服务和设备锁定**：时间控制和设备锁定管理
5. **悬浮窗服务和应用监控**：全局状态显示和用户交互
6. **白名单管理和应用分类**：应用权限分类管理
7. **互相保活机制**：服务稳定性保障
8. **WebSocket连接和消息处理**：实时通信机制
9. **完整系统交互概览**：各层级组件关系图

这些流程图清晰地展示了各个模块之间的交互关系、数据流向和决策分支，为开发和维护提供了完整的架构参考。
```
```
```
