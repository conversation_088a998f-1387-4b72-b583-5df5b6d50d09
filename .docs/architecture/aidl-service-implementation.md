# AIDL Service方案架构实现文档

## 📋 实施概述

本文档记录了TimeController项目中AIDL Service方案的完整实施过程，该方案旨在替代CheckWhenLauncherActivity，为Unity第三方应用提供稳定的应用启动权限验证服务。

## 🎯 实施目标

### 主要目标
- **消除Activity栈复杂度**：移除CheckWhenLauncherActivity，避免在startLockMode环境下的Activity栈管理问题
- **统一验证入口**：通过CheckService提供统一的应用启动权限验证服务
- **Unity友好集成**：提供同步AIDL接口，便于Unity应用集成
- **保持现有功能**：确保LockActivity登录验证流程和WebSocket通信机制正常工作

### 技术优势
- **稳定性提升**：Service比Activity在后台运行更稳定
- **接口标准化**：AIDL提供标准的跨进程通信接口
- **架构简化**：减少组件间的复杂依赖关系

## 🏗️ 架构变更对比

### 原有架构流程
```
无障碍服务监听 → shouldInterceptAppLaunch() → interceptAppLaunch() 
    ↓
启动CheckWhenLauncherActivity → CheckWhenLauncherViewModel.performVerification()
    ↓
验证结果处理 → 启动目标应用 或 启动LockActivity
```

### 新架构流程
```
无障碍服务监听 → shouldInterceptAppLaunch() → interceptAppLaunch()
    ↓
CheckService.checkAppLaunch() → 统一验证逻辑
    ↓
验证结果处理 → 启动目标应用 或 启动LockActivity
```

## 📁 新增组件

### 1. AIDL接口文件
- **ICheckService.aidl**：主要服务接口，提供应用启动权限验证方法
- **ICheckServiceCallback.aidl**：回调接口，用于状态变化通知

### 2. CheckService服务
- **位置**：`app/src/main/java/com/hwb/timecontroller/service/CheckService.kt`
- **功能**：
  - 实现ICheckService接口
  - 集成CountdownManager和WhitelistManager业务逻辑
  - 提供前台服务和保活机制
  - 支持回调监听器管理

### 3. Unity集成文档
- **位置**：`.docs/integration/unity-checkservice-integration.md`
- **内容**：完整的Unity应用集成指南和示例代码

## 🔧 核心实现细节

### CheckService核心方法

#### checkAppLaunch(String packageName)
```kotlin
private fun performAppLaunchCheck(packageName: String): Boolean {
    // 1. 检查系统应用或本应用
    if (packageName == Constant.homePackage || packageName == this.packageName) {
        return true
    }
    
    // 2. 检查倒计时状态
    if (CountdownManager.countdownData.value.state == CountdownManager.CountdownState.RUNNING) {
        return true
    }
    
    // 3. 检查白名单状态
    if (WhitelistManager.isAppInWhitelist(packageName)) {
        return true
    }
    
    // 4. 受控应用需要用户验证
    launchLockActivityForVerification(packageName)
    return false
}
```

### 服务生命周期管理
- **前台服务**：确保服务在后台稳定运行
- **保活机制**：集成MutualKeepAliveManager，与其他服务互相保活
- **异常处理**：完善的错误处理和恢复机制

### 回调机制
- **状态监听**：监听CountdownManager状态变化
- **结果通知**：向注册的客户端推送验证结果
- **连接管理**：自动管理客户端连接状态

## 🔄 架构集成点

### 1. AppLifecycleManagerService集成
- 在智能启动流程中启动CheckService
- 场景1（完整权限）和场景3（部分权限）都会启动CheckService
- 添加CheckService到保活伙伴列表

### 2. 无障碍服务改造
- 简化interceptAppLaunch()方法
- 移除CheckWhenLauncherActivity启动逻辑
- 保留应用拦截的核心判断逻辑

### 3. 保活网络更新
- 所有服务的保活伙伴列表都添加了CheckService
- 形成完整的服务保活网络

## 📊 数据流设计

### AIDL Service完整架构流程
```mermaid
flowchart TD
    subgraph "用户操作"
        A[用户点击应用图标]
        B[Unity应用调用AIDL]
    end

    subgraph "系统监听层"
        C[无障碍服务监听]
        D[AppLifecycleAccessibilityService]
    end

    subgraph "验证服务层"
        E[CheckService]
        F[ICheckService.aidl]
        G[应用启动权限验证]
    end

    subgraph "业务逻辑层"
        H[CountdownManager]
        I[WhitelistManager]
        J[倒计时状态检查]
        K[白名单检查]
    end

    subgraph "用户验证层"
        L[LockActivity]
        M[LoginViewModel]
        N[WebSocket通信]
        O[HTTP请求]
    end

    subgraph "结果处理"
        P[启动目标应用]
        Q[阻止启动]
        R[等待用户验证]
    end

    A --> C
    B --> F
    C --> D
    D --> E
    F --> E
    E --> G
    G --> H
    G --> I
    H --> J
    I --> K

    J --> |倒计时运行中| P
    K --> |白名单应用| P
    J --> |倒计时结束| L
    K --> |受控应用| L

    L --> M
    M --> N
    M --> O
    N --> |登录成功| P
    N --> |登录失败| Q
    O --> |获取二维码| L

    style E fill:#e1f5fe
    style F fill:#e8f5e8
    style L fill:#fff3e0
    style P fill:#e8f5e8
    style Q fill:#ffebee
```

### Unity集成流程
```mermaid
flowchart TD
    A[Unity应用启动] --> B[连接CheckService]
    B --> C[调用checkAppLaunch]
    C --> D{验证结果}
    D -- true --> E[启动目标应用]
    D -- false --> F[等待用户验证]
    F --> G[LockActivity显示]
    G --> H[用户完成验证]
    H --> I[回调通知Unity]
```

## 🚀 部署和测试

### 服务注册
CheckService已在AndroidManifest.xml中注册为可导出的前台服务：
```xml
<service
    android:name=".service.CheckService"
    android:exported="true"
    android:foregroundServiceType="specialUse">
    <intent-filter>
        <action android:name="com.hwb.timecontroller.service.ICheckService" />
    </intent-filter>
</service>
```

### 权限要求
- `FOREGROUND_SERVICE`：前台服务权限
- `FOREGROUND_SERVICE_SPECIAL_USE`：特殊用途前台服务权限

### 测试要点
1. **服务启动**：验证CheckService能够正常启动和运行
2. **AIDL绑定**：测试第三方应用能够成功绑定服务
3. **验证逻辑**：确保应用启动权限验证逻辑正确
4. **保活机制**：验证服务保活网络正常工作
5. **Unity集成**：测试Unity应用集成效果

## 📈 性能和稳定性

### 性能优化
- **异步处理**：回调通知使用异步机制
- **资源管理**：及时清理无效的回调监听器
- **内存优化**：使用RemoteCallbackList管理客户端连接

### 稳定性保障
- **异常捕获**：所有AIDL调用都有异常处理
- **服务恢复**：START_STICKY确保服务被杀死后自动重启
- **连接管理**：自动处理客户端连接断开和重连

## 🔮 未来扩展

### 可能的增强功能
1. **批量验证**：支持一次验证多个应用
2. **缓存机制**：缓存验证结果，提高响应速度
3. **统计功能**：记录应用启动统计信息
4. **远程配置**：支持远程更新验证规则

### 兼容性考虑
- 保持AIDL接口向后兼容
- 支持渐进式功能升级
- 维护与现有组件的兼容性

## 📝 总结

AIDL Service方案成功实现了以下目标：
- ✅ 消除了CheckWhenLauncherActivity的Activity栈复杂度问题
- ✅ 提供了统一的应用启动权限验证服务
- ✅ 为Unity应用提供了友好的集成接口
- ✅ 保持了现有LockActivity登录验证流程的完整性
- ✅ 建立了完善的服务保活网络

该方案为TimeController项目提供了更稳定、更易集成的应用启动控制能力，特别适合在startLockMode环境下运行。
