# TimeController 系统架构概览

## 系统概述
TimeController 是一个基于 Android 设备所有者权限的**应用启动控制和时间管理系统**，通过无障碍服务实现应用启动拦截、付款验证、权限自动管理和时间控制等功能。

## 核心架构

### 🏗️ 系统层次结构
```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层 (UI Layer)                      │
├─────────────────────────────────────────────────────────────┤
│ MainActivity │ CheckWhenLauncherActivity │ LockActivity │ WhitelistActivity │ 悬浮窗界面 │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Business Layer)                 │
├─────────────────────────────────────────────────────────────┤
│ CheckWhenLauncherViewModel │ PaymentViewModel │ 倒计时管理 │ 应用监控 │ 权限管理 │ 白名单管理 │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                    服务层 (Service Layer)                    │
├─────────────────────────────────────────────────────────────┤
│ AppLifecycleAccessibilityService │ FloatingWindowService │ CountdownService │ AppLifecycleManagerService │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                   工具层 (Utility Layer)                     │
├─────────────────────────────────────────────────────────────┤
│ GlobalWebSocketManager │ NetworkManager │ DeviceOwnerPermissionManager │ AccessibilityUtils │ MutualKeepAliveManager │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                  系统层 (System Layer)                       │
├─────────────────────────────────────────────────────────────┤
│ Android Framework │ Device Owner API │ Accessibility API │ Ktor Client │ WebSocket │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 📱 用户界面层
- **MainActivity**：主界面，提供倒计时设置和状态显示
- **LockActivity**：登录验证界面，显示小程序码和处理登录流程（支持LockTask模式）
- **WhitelistActivity**：白名单管理界面
- **FloatingWindow**：悬浮窗界面，全局倒计时显示
- **CheckService**：应用启动权限验证服务（AIDL接口，替代原CheckWhenLauncherActivity）

### 🧠 业务逻辑层 (MVVM架构)
- **LoginViewModel**：登录验证业务逻辑，集成HTTP和WebSocket
- **CountdownManager**：倒计时状态管理
- **WhitelistManager**：白名单应用管理
- **CheckService**：应用启动权限验证服务（AIDL接口）

### ⚙️ 服务层
- **AppLifecycleAccessibilityService**：核心无障碍服务，应用启动拦截
- **FloatingWindowService**：悬浮窗服务，全局倒计时显示
- **CountdownService**：倒计时后台服务
- **AppLifecycleManagerService**：应用生命周期管理和智能启动
- **CheckService**：应用启动权限验证服务，提供AIDL接口

### 🔧 工具层
- **GlobalWebSocketManager**：全局WebSocket连接管理和消息分发
- **NetworkManager**：统一网络客户端管理（Ktor Client）
- **DeviceOwnerPermissionManager**：设备所有者权限管理
- **AccessibilityUtils**：无障碍服务管理
- **MutualKeepAliveManager**：多服务互相保活管理
- **OverlayPermissionHelper**：悬浮窗权限自动获取

## 数据流架构

### 📊 应用启动控制数据流
```
用户点击应用 → 系统启动应用 → 无障碍服务监听
    ↓
检查是否为受控应用(!WhitelistManager.isAppInWhitelist)
    ↓
是受控应用 → CheckService.checkAppLaunch()
    ↓
统一验证逻辑:
├─ 系统应用/本应用 → 允许启动
├─ 倒计时运行中 → 允许启动
├─ 白名单应用 → 允许启动
└─ 受控应用 → 启动LockActivity进行用户验证
    ↓
用户登录验证 → 验证成功 → 启动目标应用
```

### 💰 登录验证数据流
```
LockActivity启动 → LoginViewModel初始化
    ↓
HTTP请求获取Base64小程序码 → Coil加载显示
    ↓
GlobalWebSocketManager建立连接 → 监听登录状态
    ↓
用户扫码登录 → 服务器推送成功消息
    ↓
WebSocket接收消息 → LoginViewModel处理
    ↓
登录成功 → 启动目标应用 → 结束流程
```

### 🔄 权限获取流程
```
应用启动 → 权限状态检查 → 智能启动策略
    ↓
┌─ 场景1: 完整权限 → 直接启动悬浮窗
├─ 场景2: 缺少无障碍权限 → 启动MainActivity
├─ 场景3: 缺少悬浮窗权限 → 自动获取权限 → 启动悬浮窗
└─ 其他场景 → 启动MainActivity
```

## 权限体系

### 🔐 权限层级
```
设备所有者权限 (Device Owner)
    ├─ 无障碍服务权限 (Accessibility Service)
    │   └─ 悬浮窗权限 (System Alert Window)
    ├─ 设备管理权限 (Device Admin)
    └─ 系统设置修改权限 (Write Secure Settings)
```

### 🎯 权限用途
- **Device Owner**：核心权限，用于应用控制和权限管理
- **Accessibility Service**：应用启动监控和拦截、自动权限获取
- **System Alert Window**：悬浮窗显示
- **Device Admin**：设备锁定和安全策略

## 状态管理

### 📊 系统状态定义
```kotlin
// 应用启动验证结果
enum class VerificationResult {
    ALLOW_LAUNCH,           // 允许启动
    REQUIRE_PAYMENT,        // 需要付款验证
    BLOCKED_BY_COUNTDOWN,   // 被倒计时阻止
    BLOCKED_BY_LIMIT        // 被次数限制阻止
}

// 付款状态
enum class PaymentState {
    IDLE,                   // 空闲状态
    LOADING_QR,            // 加载小程序码
    QR_LOADED,             // 小程序码已加载
    WAITING_PAYMENT,       // 等待付款
    PAYMENT_SUCCESS,       // 付款成功
    PAYMENT_FAILED,        // 付款失败
    ERROR                  // 错误状态
}

// WebSocket连接状态
enum class WebSocketState {
    DISCONNECTED,          // 未连接
    CONNECTING,            // 连接中
    CONNECTED,             // 已连接
    ERROR                  // 连接错误
}

// 应用系统状态
data class AppSystemState(
    val isCountdownActive: Boolean,
    val remainingTime: Long,
    val isDeviceLocked: Boolean,
    val currentForegroundApp: String?,
    val hasOverlayPermission: Boolean,
    val hasAccessibilityPermission: Boolean,
    val isDeviceOwner: Boolean,
    val webSocketState: WebSocketState,
    val paymentState: PaymentState
)
```

### 🔄 状态同步机制
- **MMKV**：高性能键值存储，持久化应用状态和配置
- **LiveData**：MVVM架构中的响应式数据绑定
- **WebSocket**：实时付款状态推送
- **广播机制**：服务间状态通信
- **观察者模式**：UI状态实时更新

## 技术栈

### 🔧 核心技术栈
```kotlin
// 网络通信
implementation "io.ktor:ktor-client-android:$ktor_version"
implementation "io.ktor:ktor-client-websockets:$ktor_version"
implementation "io.ktor:ktor-client-logging:$ktor_version"

// 图片加载
implementation "io.coil-kt:coil:$coil_version"

// 数据存储
implementation "com.tencent:mmkv:$mmkv_version"

// 日志系统
implementation "com.jakewharton.XLog.XLog.$XLog.version"

// 架构组件
implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
```

### 🏗️ 架构模式
- **MVVM**：Model-View-ViewModel架构模式
- **Repository模式**：数据访问层抽象
- **观察者模式**：状态变化通知
- **单例模式**：全局管理器类
- **工厂模式**：网络客户端创建

## 安全机制

### 🛡️ 安全策略
1. **权限验证**：所有关键操作前验证设备所有者权限
2. **LockTask模式**：验证和付款界面使用LockTask防止退出
3. **应用启动拦截**：实时监控并拦截受控应用启动
4. **数据保护**：MMKV加密存储敏感配置
5. **WebSocket安全**：付款状态推送使用安全连接

### 🔒 锁定机制
- **CheckService**：AIDL服务，提供应用启动权限验证接口
- **LockActivity**：付款验证界面，支持LockTask模式
- **应用启动拦截**：通过无障碍服务拦截应用启动
- **系统级权限**：使用设备所有者权限进行系统级控制

## 性能优化

### ⚡ 优化策略
1. **多服务保活**：MutualKeepAliveManager确保核心服务持续运行
2. **内存管理**：ViewModel生命周期管理，及时释放资源
3. **网络优化**：Ktor Client连接池，WebSocket长连接复用
4. **响应速度**：异步处理验证逻辑，避免阻塞UI线程
5. **图片加载**：Coil异步加载Base64图片，内存缓存优化

### 📊 监控指标
- **服务存活率**：核心服务的运行稳定性（通过心跳机制监控）
- **权限获取成功率**：自动权限获取的成功率
- **应用拦截准确率**：应用启动拦截的准确性
- **付款验证成功率**：WebSocket通信和付款流程的成功率
- **用户体验**：界面响应速度和操作流畅度

## 扩展性设计

### 🔧 模块化架构
- **MVVM分层**：清晰的业务逻辑和UI分离
- **管理器模式**：各功能模块独立管理器
- **接口抽象**：网络、存储、权限等核心功能接口化
- **配置化**：白名单、验证规则等支持动态配置

### 🚀 未来规划
- **云端配置**：支持远程配置白名单和验证规则
- **多设备管理**：支持多设备统一管理和状态同步
- **数据分析**：应用使用行为分析和报告
- **支付方式扩展**：支持更多支付方式和验证机制



## 总结

TimeController 通过精心设计的架构实现了**应用启动控制和时间管理**的完整解决方案。系统采用现代化的MVVM架构，集成了应用启动拦截、付款验证、WebSocket实时通信等先进功能，为用户提供了强大而稳定的应用管理体验。

### 🎯 核心优势
- **功能完整**：涵盖应用启动控制、付款验证、时间管理、权限自动化等核心功能
- **架构先进**：MVVM架构 + Ktor网络框架 + WebSocket实时通信
- **性能优化**：多服务保活、异步处理、内存优化等多项优化策略
- **用户体验**：透明验证界面、实时付款状态、智能权限获取
- **安全可靠**：LockTask模式、设备所有者权限、加密存储等安全机制
- **扩展性强**：模块化设计，支持功能扩展和配置定制
