# TimeController 应用启动控制完整流程图

## 系统启动与权限初始化流程

```mermaid
flowchart TD
    %% 系统启动阶段
    Start[设备开机/应用启动] --> AppLifecycleManagerService[AppLifecycleManagerService启动]
    AppLifecycleManagerService --> CheckPermissions[检查权限状态]
    
    CheckPermissions --> CheckOwner{设备所有者权限?}
    CheckOwner -->|否| Scenario2[场景2: 启动MainActivity<br/>引导用户设置权限]
    CheckOwner -->|是| CheckA11y{无障碍服务权限?}
    
    CheckA11y -->|否| Scenario2
    CheckA11y -->|是| CheckFloat{悬浮窗权限?}
    
    CheckFloat -->|否| Scenario3[场景3: 自动获取悬浮窗权限<br/>通过DeviceOwner或无障碍服务]
    CheckFloat -->|是| Scenario1[场景1: 完整权限<br/>直接启动悬浮窗服务]
    
    Scenario1 --> FloatingWindowService[启动FloatingWindowService<br/>显示悬浮窗倒计时]
    Scenario2 --> MainActivity[启动MainActivity<br/>权限管理界面]
    Scenario3 --> AutoGrantFloat[自动授予悬浮窗权限]
    AutoGrantFloat --> FloatingWindowService
    
    FloatingWindowService --> ServicesReady[核心服务就绪<br/>互相保活机制启动]
    MainActivity --> ManualSetup[用户手动设置权限]
    ManualSetup --> ServicesReady
    
    ServicesReady --> SystemReady[系统就绪，返回桌面]
```

## 应用启动拦截与验证流程

```mermaid
flowchart TD
    %% 应用启动监听
    SystemReady[系统就绪] --> UserClick[用户点击应用图标]
    UserClick --> A11yService[AppLifecycleAccessibilityService<br/>监听窗口状态变化]
    
    A11yService --> CheckGovernance{管控状态开启?}
    CheckGovernance -->|否| AllowLaunch[允许正常启动]
    CheckGovernance -->|是| CheckUnconditional{无条件白名单?}
    
    CheckUnconditional -->|是| AllowLaunch
    CheckUnconditional -->|否| CheckSpecialActivity{LockActivity?}

    CheckSpecialActivity -->|是| AllowLaunch
    CheckSpecialActivity -->|否| InterceptLaunch[拦截应用启动<br/>调用CheckService]

    %% CheckService验证流程
    InterceptLaunch --> CheckService[CheckService<br/>AIDL验证服务]
    CheckService --> StartVerification[CheckService.performCheck<br/>开始验证流程]
    
    StartVerification --> CheckHomeOrSelf{homePackage或本应用?}
    CheckHomeOrSelf -->|是| AllowLaunch
    CheckHomeOrSelf -->|否| CheckWhitelist{应用在白名单中?}
    
    CheckWhitelist -->|否| BlockedByLimit[BLOCKED_BY_LIMIT<br/>非白名单应用被阻止]
    CheckWhitelist -->|是| CheckCountdown{倒计时正在运行?}
    
    CheckCountdown -->|是| AllowLaunch
    CheckCountdown -->|否| RequirePayment[REQUIRE_PAYMENT<br/>需要登录验证]
    
    BlockedByLimit --> FinishVerification[结束验证，返回桌面]
    RequirePayment --> LaunchLockActivity[启动LockActivity<br/>登录验证界面]
```

## 登录验证与应用启动流程

```mermaid
flowchart TD
    %% LockActivity登录流程
    LaunchLockActivity[LockActivity启动] --> InitLockTask[启动LockTask模式<br/>防止用户退出]
    InitLockTask --> StartLoginFlow[LoginViewModel<br/>开始登录流程]
    
    StartLoginFlow --> ConnectWebSocket[GlobalWebSocketManager<br/>建立WebSocket连接]
    ConnectWebSocket --> HttpRequest[HTTP请求获取二维码<br/>POST /api/payment/qrcode]
    
    HttpRequest --> CheckHttpResponse{HTTP请求成功?}
    CheckHttpResponse -->|否| ShowError[显示错误信息<br/>LoginState.ERROR]
    CheckHttpResponse -->|是| ParseQRResponse[解析QRCodeResponse<br/>获取orderId和base64]
    
    ParseQRResponse --> DisplayQR[Coil加载显示二维码<br/>LoginState.WAITING_LOGIN]
    DisplayQR --> ListenWebSocket[监听WebSocket消息<br/>messageFlow.collect]
    
    ListenWebSocket --> ReceiveMessage{收到WebSocket消息}
    ReceiveMessage -->|login_status| HandleLoginStatus[处理登录状态更新]
    ReceiveMessage -->|heartbeat| KeepAlive[心跳保持连接]
    ReceiveMessage -->|error| ShowError
    
    HandleLoginStatus --> CheckLoginStatus{登录状态判断}
    CheckLoginStatus -->|PENDING| WaitingLogin[等待用户扫码]
    CheckLoginStatus -->|SUCCESS| LoginSuccess[LoginState.SUCCESS<br/>登录成功]
    CheckLoginStatus -->|FAILED| LoginFailed[LoginState.FAILED<br/>登录失败]
    CheckLoginStatus -->|EXPIRED| LoginExpired[LoginState.EXPIRED<br/>二维码过期]
    
    LoginSuccess --> UpdateUserStatus[UserManager更新用户状态<br/>setUserLoggedIn(true)]
    UpdateUserStatus --> LaunchTargetApp[启动目标应用<br/>stopLockTask并finish]
    
    LoginFailed --> ShowRetry[显示重试选项]
    LoginExpired --> ShowRefresh[显示刷新二维码选项]
    ShowError --> ShowRetry
    
    ShowRetry --> RefreshQR[用户点击刷新]
    ShowRefresh --> RefreshQR
    RefreshQR --> StartLoginFlow
    
    %% 用户操作选项
    DisplayQR --> UserCancel{用户取消操作}
    UserCancel -->|取消按钮| SmartNavigation[智能返回逻辑<br/>返回homePackage或桌面]
    UserCancel -->|底部导航-主页| NavigateHome[前往homePackage]
    UserCancel -->|底部导航-设置| NavigateMain[前往MainActivity<br/>需要密码验证]
    
    SmartNavigation --> EndFlow[结束流程]
    NavigateHome --> EndFlow
    NavigateMain --> EndFlow
    LaunchTargetApp --> EndFlow
```

## 核心组件说明

### 1. AppLifecycleManagerService
- **功能**: 系统启动时的智能权限检测和服务启动管理
- **权限检测场景**:
  - 场景1: 完整权限 → 直接启动悬浮窗服务
  - 场景2: 缺少权限 → 启动MainActivity引导用户
  - 场景3: 部分权限 → 自动获取缺失权限

### 2. AppLifecycleAccessibilityService
- **功能**: 监听应用启动和窗口状态变化
- **拦截条件**:
  - 管控状态已开启 (AdminManager.isGovernanceState)
  - 非无条件白名单应用
  - 非LockActivity

### 3. CheckService
- **功能**: AIDL验证服务，快速判断应用启动权限
- **验证逻辑**:
  - homePackage和本应用 → 直接允许
  - 非白名单应用 → 阻止启动
  - 白名单应用 + 倒计时运行中 → 允许启动
  - 白名单应用 + 倒计时结束 → 启动LockActivity进行登录验证

### 4. LockActivity
- **功能**: 登录验证界面，支持LockTask模式防止退出
- **登录流程**:
  - HTTP获取二维码 (POST /api/payment/qrcode)
  - WebSocket实时监听登录状态
  - 登录成功后更新用户状态并启动目标应用

### 5. 白名单机制
- **无条件白名单**: 输入法、homePackage、本应用等系统关键应用
- **用户白名单**: 用户自定义添加的允许应用
- **验证逻辑**: 受控应用定义为 !WhitelistManager.isAppInWhitelist(packageName)

### 6. 倒计时机制
- **状态管理**: CountdownManager使用StateFlow管理倒计时状态
- **验证逻辑**: 倒计时运行中的白名单应用可直接启动
- **结束处理**: 倒计时结束后需要登录验证才能启动应用

## 技术实现要点

### 1. 服务架构
- CheckService: AIDL应用启动验证服务
- LoginViewModel: 登录验证业务逻辑，集成HTTP长轮询

### 2. 网络通信
- **HTTP**: Ktor Client用于API请求
- **WebSocket**: GlobalWebSocketManager提供实时消息推送
- **图片加载**: Coil库用于二维码显示

### 3. 用户管理
- **UserManager**: 单例模式管理用户登录状态
- **存储策略**: MMKV主存储 + MediaStore备份
- **设备标识**: UUIDv7算法生成设备ID

### 4. 服务保活
- **MutualKeepAliveManager**: 互相保活机制
- **核心服务**: AppLifecycleAccessibilityService、FloatingWindowService、CountdownService

### 5. LockTask模式
- **防退出机制**: LockActivity使用startLockTask防止用户退出
- **智能导航**: 支持返回homePackage或MainActivity的智能导航逻辑
