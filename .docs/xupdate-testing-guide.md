# XUpdate功能测试指南

## 📋 测试概述

本文档提供XUpdate版本更新功能的完整测试指南，确保所有功能正常工作。

## 🧪 测试环境准备

### 1. 服务器接口准备
确保更新检查接口正常工作：
- **URL**: `https://game.3ddu.cn/estate/v1/checkUpdate`
- **方法**: GET
- **返回格式**: 
```json
{
  "code": 200,
  "msg": "更新信息",
  "rspdata": {
    "latestVersion": "1.2.3",
    "downloadUrl": "https://example.com/app-latest.apk",
    "forceUpdate": true,
    "updateContent": "修复已知问题，提升稳定性"
  },
  "jwt": "",
  "rsptime": 1752737858
}
```

### 2. 测试APK准备
- 准备不同版本号的APK文件
- 确保下载链接可访问
- 验证APK文件完整性

## 🔍 功能测试用例

### 测试用例1：启动时自动检查
**目标**: 验证应用启动时的自动更新检查

**步骤**:
1. 启动应用
2. 等待5秒
3. 观察日志输出

**预期结果**:
- 日志显示"开始启动时更新检查"
- 如果有强制更新，显示提示并弹出更新对话框
- 如果无更新，日志显示"启动检查：无强制更新"

### 测试用例2：手动检查更新
**目标**: 验证手动点击检查更新功能

**步骤**:
1. 打开MainActivity
2. 点击"检查更新"按钮
3. 观察界面反应

**预期结果**:
- 显示"正在检查更新..."提示
- 弹出XUpdate更新对话框（如有更新）
- 或显示"当前已是最新版本"（无更新时）

### 测试用例3：版本比较逻辑
**目标**: 验证版本号比较的准确性

**测试数据**:
- 当前版本: 1.0.0
- 服务器版本: 1.0.1 (应该更新)
- 服务器版本: 1.0.0 (不需要更新)
- 服务器版本: 0.9.9 (不需要更新)

**预期结果**:
- 正确识别是否需要更新
- 日志记录版本比较结果

### 测试用例4：强制更新
**目标**: 验证强制更新功能

**步骤**:
1. 服务器返回`forceUpdate: true`
2. 启动应用或手动检查更新
3. 观察更新对话框

**预期结果**:
- 更新对话框不可取消
- 显示强制更新相关提示
- 用户必须更新才能继续使用

### 测试用例5：网络状态检查
**目标**: 验证网络检查功能

**步骤**:
1. 断开网络连接
2. 点击"检查更新"
3. 观察日志输出

**预期结果**:
- 日志显示"网络不可用，无法检查更新"
- 不发起网络请求

### 测试用例6：错误处理
**目标**: 验证各种错误情况的处理

**测试场景**:
- 服务器返回错误状态码
- 服务器返回格式错误的JSON
- 网络超时
- 下载链接无效

**预期结果**:
- 应用不崩溃
- 显示合适的错误提示
- 记录详细的错误日志

## 🔧 调试工具

### 1. 日志查看
使用以下命令查看日志：
```bash
adb logcat | grep -E "(XUpdate|UpdateManager|XLog."
```

### 2. 关键日志标识
- `开始启动时更新检查` - 启动检查开始
- `发现新版本` - 检测到更新
- `当前已是最新版本` - 无需更新
- `检测到强制更新` - 强制更新提醒
- `网络不可用` - 网络检查失败

### 3. 调试模式
在debug版本中，XUpdate会输出详细的调试信息。

## 📊 测试检查清单

### 基础功能
- [ ] 应用启动时自动检查更新
- [ ] 手动点击检查更新
- [ ] 版本号比较正确
- [ ] 更新对话框正常显示
- [ ] 下载功能正常
- [ ] 安装功能正常

### 高级功能
- [ ] 强制更新检测
- [ ] 网络状态检查
- [ ] 错误处理完善
- [ ] 日志记录详细
- [ ] UI主题正确
- [ ] 后台更新支持

### 异常情况
- [ ] 网络断开时的处理
- [ ] 服务器错误时的处理
- [ ] JSON格式错误时的处理
- [ ] 下载失败时的处理
- [ ] 安装失败时的处理

## 🐛 常见问题解决

### 问题1：检查更新无反应
**可能原因**:
- 网络连接问题
- 服务器接口异常
- 权限不足

**解决方法**:
1. 检查网络连接
2. 验证服务器接口
3. 检查应用权限

### 问题2：下载失败
**可能原因**:
- 存储权限不足
- 下载链接无效
- 网络不稳定

**解决方法**:
1. 授予存储权限
2. 验证下载链接
3. 检查网络稳定性

### 问题3：安装失败
**可能原因**:
- 安装权限不足
- APK文件损坏
- 签名不匹配

**解决方法**:
1. 授予安装权限
2. 重新下载APK
3. 检查APK签名

## 📈 性能测试

### 1. 启动性能
- 测量启动检查对应用启动时间的影响
- 确保5秒延迟后才开始检查

### 2. 网络性能
- 测试不同网络环境下的检查速度
- 验证超时处理机制

### 3. 内存使用
- 监控更新过程中的内存使用
- 确保无内存泄漏

## ✅ 测试完成标准

所有测试用例通过，且满足以下条件：
1. 功能完整性：所有功能正常工作
2. 稳定性：无崩溃和异常
3. 用户体验：界面友好，操作流畅
4. 性能：不影响应用正常使用
5. 兼容性：支持目标Android版本

---

**文档版本**: v1.0  
**更新时间**: 2025-01-17  
**适用版本**: XUpdate 2.1.5
