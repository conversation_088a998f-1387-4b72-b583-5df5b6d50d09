# 远程白名单更新功能实现文档

## 📋 功能概述

实现了基于HTTP API的远程白名单自动更新功能，支持从指定接口拉取应用白名单并同步到本地用户自定义白名单中。

## 🎯 实现目标

- ✅ 在两个指定时间节点异步拉取远程白名单
- ✅ 强制10分钟以上更新间隔控制
- ✅ 自动同步LockTask白名单
- ✅ 完整的错误处理和日志记录
- ✅ 不影响现有白名单逻辑

## 🔧 技术实现

### 1. API数据模型

**文件**: `app/src/main/java/com/hwb/timecontroller/network/ApiResponse.kt`

```kotlin
@Serializable
data class RemoteAppInfo(
    val id: String,
    val appName: String,
    val packageName: String
)

typealias RemoteWhitelistResponse = List<RemoteAppInfo>
```

### 2. 网络配置

**文件**: `app/src/main/java/com/hwb/timecontroller/network/NetworkConfig.kt`

```kotlin
// 远程白名单接口
const val REMOTE_WHITELIST_BASE_URL = "http://vr.infinitus-test.cn"
const val ENDPOINT_REMOTE_WHITELIST = "/VR/GetApp.php"

// 远程白名单更新配置
const val REMOTE_WHITELIST_UPDATE_INTERVAL_MS = 10 * 60 * 1000L // 10分钟
```

### 3. 核心更新逻辑

**文件**: `app/src/main/java/com/hwb/timecontroller/business/WhitelistManager.kt`

#### 主要方法

- `fetchAndUpdateRemoteWhitelist(context: Context?)` - 主入口方法
- `shouldUpdateRemoteWhitelist()` - 检查更新间隔
- `updateUserWhitelistFromRemote()` - 更新本地白名单
- `recordRemoteWhitelistUpdateTime()` - 记录更新时间

#### 关键特性

- **时间控制**: 使用内存变量存储上次更新时间，强制10分钟间隔
- **异步执行**: 使用协程在IO线程执行，不阻塞UI
- **LockTask同步**: 更新后自动调用`updateLockTaskPackages()`
- **HTTP支持**: 创建专门的HTTP客户端支持明文通信
- **资源管理**: 自动关闭HTTP客户端防止内存泄漏
- **详细日志**: 记录更新过程、新增应用信息

### 4. 网络安全配置

**文件**: `app/src/main/res/xml/network_security_config.xml`

```xml
<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <!-- 允许明文HTTP通信的域名配置 -->
    <domain-config cleartextTrafficPermitted="true">
        <!-- 远程白名单API域名 -->
        <domain includeSubdomains="true">vr.infinitus-test.cn</domain>
    </domain-config>

    <!-- 默认配置：其他域名仍然要求HTTPS -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <!-- 信任系统证书 -->
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
```

**AndroidManifest.xml配置**:
```xml
<application
    android:networkSecurityConfig="@xml/network_security_config"
    ... >
```

### 5. 调用时机

**文件**: `app/src/main/java/com/hwb/timecontroller/activity/EmptyActivity.kt`

#### 位置1: startNextActivity方法
```kotlin
// 启动homePackage进入LockTask管控模式
WhitelistManager.openHomeApp()

// 异步更新远程白名单
WhitelistManager.fetchAndUpdateRemoteWhitelist(this)
```

#### 位置2: onCreate方法最后
```kotlin
// 异步更新远程白名单（放在onCreate最后，确保Device Owner权限已验证）
WhitelistManager.fetchAndUpdateRemoteWhitelist(this)
```

## 📊 API接口规范

### 请求信息
- **URL**: `http://vr.infinitus-test.cn/VR/GetApp.php`
- **方法**: GET
- **参数**: 无

### 响应格式
```json
[
  {
    "id": "1",
    "appName": "五子棋",
    "packageName": "com.YHB.wuziqi"
  },
  {
    "id": "2", 
    "appName": "跑得快",
    "packageName": "com.qiuyao.PDK0217"
  }
]
```

## 🔄 工作流程

1. **触发条件**: EmptyActivity的onCreate和startNextActivity方法
2. **间隔检查**: 验证距离上次更新是否超过10分钟
3. **网络请求**: 使用专用HTTP客户端发起GET请求
4. **响应处理**: 先获取原始文本，处理Content-Type兼容性问题
5. **文本清理**: 移除BOM字符和不可见字符，确保JSON格式正确
6. **数据解析**: 手动解析清理后的JSON响应为RemoteAppInfo列表
6. **白名单更新**: 将packageName添加到用户自定义白名单
7. **LockTask同步**: 自动更新设备管理器的LockTask白名单
8. **状态记录**: 保存更新时间，记录详细日志

## 🛡️ 错误处理

- **网络异常**: 分类处理不同类型的网络错误（连接失败、超时、DNS解析等）
- **JSON解析错误**: 先获取原始文本，手动解析JSON，处理Content-Type兼容性
- **BOM字符处理**: 自动移除UTF-8/UTF-16 BOM字符，确保JSON解析成功
- **服务器响应**: 处理服务器返回text/html而非application/json的情况
- **权限问题**: 在Device Owner权限验证后执行
- **重复更新**: 时间间隔控制防止频繁请求
- **HTTP明文通信**: 通过网络安全配置允许特定域名的HTTP访问
- **资源泄漏**: 使用try-finally确保HTTP客户端正确关闭

## 📝 日志记录

- 更新开始/完成状态
- 获取的应用数量
- 新增应用的详细信息
- 错误信息和异常堆栈

## 🔍 存储机制

- **白名单数据**: 存储在MMKV的`customer_whitelist_packages`键中
- **更新时间**: 存储在内存变量`lastRemoteWhitelistUpdateTime`中
- **数据格式**: Set<String>格式存储包名列表
- **时间重置**: 应用重启后更新时间重置为0，首次调用会立即更新

## ⚡ 性能优化

- 异步执行，不阻塞主线程
- 时间间隔控制，避免频繁网络请求
- 增量更新，只添加新的包名
- 内存友好的数据结构

## 🔧 维护说明

- 如需修改更新间隔，调整`NetworkConfig.REMOTE_WHITELIST_UPDATE_INTERVAL_MS`
- 如需修改API地址，更新`NetworkConfig`中的相关常量
- 日志级别可通过XLog.置调整
- 支持运行时动态调用`fetchAndUpdateRemoteWhitelist()`方法

## 📋 测试建议

1. 验证10分钟间隔控制是否生效
2. 测试网络异常情况的处理
3. 确认LockTask白名单同步功能
4. 验证新增应用是否正确添加到白名单
5. 检查日志输出的完整性和准确性
6. 测试BOM字符处理是否正常工作
7. 验证不同编码格式的服务器响应兼容性

## 🐛 常见问题解决

### JSON解析失败
- **问题**: `JsonDecodingException` 或 `NoTransformationFoundException`
- **原因**: 服务器响应包含BOM字符或Content-Type不正确
- **解决**: 已自动处理BOM字符清理和手动JSON解析

### 网络连接失败
- **问题**: `UnknownServiceException: CLEARTEXT communication not permitted`
- **原因**: Android网络安全策略不允许HTTP明文通信
- **解决**: 已配置网络安全策略允许特定域名的HTTP访问
