# 登录页面刷新按钮功能实现

## 概述

为登录页面（LockActivity）在取消按钮旁边添加了刷新按钮，用户可以主动调用一次登录状态查询接口，无需等待轮询机制。

## 实现内容

### 1. 样式文件创建

#### modern_button_refresh.xml
```xml
<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/lock_glow_blue">
    <!-- 蓝色渐变按钮样式，与取消按钮的红色形成对比 -->
    <!-- 包含按下状态、正常状态的完整视觉效果 -->
    <!-- 采用Material Design风格的阴影和高光效果 -->
</ripple>
```

**特点**：
- 蓝色渐变设计（#4299E1 → #3182CE）
- 与现有按钮保持一致的Material Design风格
- 包含外发光、阴影、高光等视觉效果
- 支持按下状态的视觉反馈

### 2. 布局文件修改

#### activity_lock.xml
```xml
<!-- 操作按钮区域 -->
<LinearLayout
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginTop="24dp">

    <Button
        android:id="@+id/btn_refresh_qr"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:text="@string/refresh_login_qr"
        android:textSize="14sp"
        android:background="@drawable/modern_button_refresh"
        android:textColor="@color/lock_on_primary"
        android:layout_marginEnd="16dp" />

    <Button
        android:id="@+id/btn_cancel_payment"
        android:layout_width="120dp"
        android:layout_height="wrap_content"
        android:text="@string/cancel_login"
        android:textSize="14sp"
        android:background="@drawable/modern_button_cancel"
        android:textColor="@color/lock_on_primary" />
</LinearLayout>
```

**布局特点**：
- 水平排列：刷新按钮在左，取消按钮在右
- 统一尺寸：两个按钮都是120dp宽度
- 适当间距：按钮间距16dp
- 文本引用：使用strings.xml中的@string/refresh_login_qr

### 3. ViewModel功能扩展

#### LoginViewModel.kt
```kotlin
/**
 * 手动刷新登录状态（主动查询一次）
 */
fun refreshLoginStatus() {
    XLog.d("用户手动刷新登录状态")

    viewModelScope.launch {
        try {
            // 调用clientLogin接口进行一次查询
            val fullUrl = NetworkConfig.BASE_URL + NetworkConfig.BASE_PATH + "/" + NetworkConfig.ENDPOINT_CLIENT_LOGIN
            val response = httpClient.get(fullUrl) {
                parameter("secretKey", getDeviceId())
            }

            if (response.status == HttpStatusCode.OK) {
                val clientLoginResponse = response.body<ClientLoginResponse>()
                handleClientLoginResponse(clientLoginResponse)
                XLog.d("手动刷新登录状态成功")
            } else {
                XLog.w("手动刷新登录状态失败: ${response.status}")
                _errorMessage.value = "刷新失败，请稍后重试"
            }

        } catch (e: Exception) {
            XLog.e("手动刷新登录状态异常", e)
            _errorMessage.value = "网络异常，请检查网络连接"
        }
    }
}
```

**功能流程**：
1. 主动调用clientLogin接口进行一次查询
2. 使用与轮询相同的URL和参数（secretKey为设备ID）
3. 调用`handleClientLoginResponse()`处理响应结果
4. 提供错误处理和用户反馈

### 4. Activity逻辑更新

#### LockActivity.kt
```kotlin
// 属性声明
private lateinit var btnRefreshQr: Button

// 初始化
private fun initViews() {
    // ... 其他初始化
    btnRefreshQr = findViewById(R.id.btn_refresh_qr)
}

// 点击事件处理
private fun setupClickListeners() {
    // 刷新登录状态
    btnRefreshQr.setOnClickListener {
        loginViewModel.refreshLoginStatus()
    }

    // ... 其他点击事件
}
```

**集成特点**：
- 按钮属性声明和初始化
- 简洁的点击事件处理
- 直接调用ViewModel的刷新方法
- 与现有代码结构保持一致

## 功能特点

### 用户体验
- **主动控制**：用户可以主动查询登录状态，无需等待自动轮询
- **即时反馈**：点击后立即发起查询请求
- **操作直观**：按钮位置合理，操作逻辑清晰

### 技术实现
- **直接查询**：主动调用clientLogin接口进行一次状态查询
- **状态管理**：通过ViewModel统一管理登录状态
- **错误处理**：提供网络异常和请求失败的错误反馈
- **日志记录**：完整的操作日志用于调试

### 视觉设计
- **风格一致**：与现有UI风格保持一致
- **颜色对比**：蓝色刷新按钮与红色取消按钮形成良好对比
- **Material Design**：遵循Material Design设计规范
- **响应式**：支持按下状态的视觉反馈

## 文件修改清单

### 新增文件
- ✅ `app/src/main/res/drawable/modern_button_refresh.xml` - 刷新按钮样式

### 修改文件
- ✅ `app/src/main/res/layout/activity_lock.xml` - 添加刷新按钮到布局
- ✅ `app/src/main/java/com/hwb/timecontroller/viewModel/LoginViewModel.kt` - 添加刷新功能
- ✅ `app/src/main/java/com/hwb/timecontroller/activity/LockActivity.kt` - 添加按钮处理逻辑

### 依赖资源
- 使用现有的 `@string/refresh_login_qr` 字符串资源
- 使用现有的颜色资源（lock_glow_blue、lock_on_primary等）
- 继承现有的Material Design主题

## 使用说明

1. **用户操作**：在登录页面点击"刷新"按钮
2. **系统响应**：立即发起一次clientLogin接口查询请求
3. **视觉反馈**：如果用户已登录，会立即跳转到用户信息页面
4. **状态更新**：如果查询失败，会显示相应的错误信息

## 技术细节

### 刷新机制
- 直接调用clientLogin接口进行一次查询
- 使用与轮询相同的URL和参数（secretKey为设备ID）
- 调用`handleClientLoginResponse()`处理响应，与轮询逻辑保持一致

### 状态管理
- 查询成功时会更新LoginState状态
- 通过LiveData机制通知UI更新
- 保持与现有登录流程的状态一致性

### 错误处理
- 网络请求异常会显示"网络异常，请检查网络连接"
- HTTP状态码错误会显示"刷新失败，请稍后重试"
- 用户可以多次点击刷新按钮进行重试

## 总结

登录页面刷新按钮功能已完全实现，为用户提供了主动查询登录状态的能力，提升了用户体验。实现过程中保持了代码的一致性和可维护性，遵循了现有的架构模式和设计规范。用户无需等待轮询机制，可以主动检查是否已完成登录验证。
