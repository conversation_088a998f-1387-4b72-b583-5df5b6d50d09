# 用户信息页面功能实现文档

## 📋 实现概述

本次实现了用户信息页面功能，包括登录成功后的页面跳转、用户信息展示和退出登录功能。主要变更包括移除LockActivity登录成功弹窗，直接跳转到用户信息页面，并提供完整的用户信息管理功能。

## 🎯 核心功能

### 1. 登录成功流程优化
- **移除弹窗**：LockActivity登录成功后不再显示弹窗
- **直接跳转**：登录成功后直接跳转到UserInfoActivity
- **数据传递**：通过Intent传递ClientLoginData用户数据

### 2. 用户信息页面
- **用户头像**：使用Coil库加载网络头像，支持圆形裁剪
- **用户昵称**：显示用户名称，支持默认值
- **用户余额**：显示用户余额，带金币图标
- **返回按钮**：支持返回上一页面
- **退出登录**：提供退出登录功能

### 3. 退出登录功能
- **确认对话框**：使用DialogX显示确认对话框
- **本地清理**：清除本地用户信息和登录状态
- **状态管理**：通过UserManager统一管理登录状态

### 4. 悬浮窗用户信息入口
- **动态显示**：根据用户登录状态动态显示/隐藏用户信息按钮
- **状态监听**：实现UserLoginStatusListener监听登录状态变化
- **快速访问**：用户可以从悬浮窗快速进入用户信息页面
- **数据完整性**：优先使用完整的ClientLoginData，降级使用基本UserInfo

## 📁 新增文件

### Activity & ViewModel
- `app/src/main/java/com/hwb/timecontroller/activity/UserInfoActivity.kt`
- `app/src/main/java/com/hwb/timecontroller/viewModel/UserInfoViewModel.kt`

### 业务逻辑
- `app/src/main/java/com/hwb/timecontroller/business/UserLoginStatusListener.kt`

### 布局文件
- `app/src/main/res/layout/activity_user_info.xml`

### 资源文件
- `app/src/main/res/drawable/ic_arrow_back.xml` - 返回箭头图标
- `app/src/main/res/drawable/ic_default_avatar.xml` - 默认头像图标
- `app/src/main/res/drawable/ic_money.xml` - 金钱图标
- `app/src/main/res/drawable/ic_user.xml` - 用户图标（悬浮窗按钮）
- `app/src/main/res/drawable/circle_background.xml` - 圆形背景
- `app/src/main/res/drawable/balance_background.xml` - 余额背景
- `app/src/main/res/drawable/logout_button_background.xml` - 退出登录按钮背景

## 🔧 修改文件

### 核心逻辑修改
1. **LoginViewModel.kt**
   - 添加`navigateToUserInfo` LiveData事件
   - 修改`handleClientLoginResponse`方法，触发跳转事件
   - 添加`clearNavigateToUserInfo`方法

2. **LockActivity.kt**
   - 移除`showLoginSuccessDialog`方法
   - 添加`navigateToUserInfoActivity`方法
   - 修改登录成功处理逻辑
   - 添加跳转事件观察者

3. **UserManager.kt**
   - 添加`logout`方法，统一处理退出登录逻辑

4. **UserManager.kt**
   - 添加`logout`方法，统一处理退出登录逻辑
   - 添加`setClientLoginData`和`getClientLoginData`方法
   - 添加用户登录状态监听器机制

5. **FloatingWindowService.kt**
   - 添加用户信息按钮到悬浮窗
   - 实现UserLoginStatusListener接口
   - 根据登录状态动态显示/隐藏用户信息按钮

6. **ClientLoginData.kt**
   - 实现`JavaSerializable`接口，支持Intent传递

### 配置文件修改
1. **AndroidManifest.xml**
   - 注册UserInfoActivity，设置launchMode为singleTop

2. **colors.xml**
   - 添加用户信息页面相关颜色定义

3. **layout_floating_window.xml**
   - 添加用户信息按钮到悬浮窗布局

## 🚀 代码优化改进

### 1. API兼容性改进
- **全屏模式**：使用WindowInsetsController API (Android 11+) 和向下兼容
- **序列化**：支持Android 13+的类型安全getSerializableExtra API

### 2. 数据验证增强
- **用户名**：长度限制（最多20字符），防止UI溢出
- **余额**：范围检查（0-999999），防止显示异常数值
- **头像URL**：完整的加载状态监听和错误处理

### 3. 异常处理完善
- **数据提取**：try-catch包装Intent数据提取
- **图片加载**：Coil加载监听器和异常捕获
- **用户操作**：所有用户交互都有错误处理机制

## 🎨 UI设计特点

### 用户信息页面设计
- **渐变背景**：使用login_background_gradient保持视觉一致性
- **卡片布局**：用户信息使用CardView展示，提供阴影效果
- **圆形头像**：120dp大小，支持网络图片加载和圆形裁剪
- **金币余额**：带图标的余额显示，使用金色主题
- **退出按钮**：红色主题，位于页面底部

### 视觉元素
- **头像加载**：使用Coil库，支持占位图和错误图
- **圆形变换**：CircleCropTransformation实现圆形头像
- **响应式设计**：支持不同屏幕尺寸适配

## 📊 数据流程

### 登录成功流程
```
LockActivity登录成功 
→ LoginViewModel.handleClientLoginResponse 
→ 触发navigateToUserInfo事件 
→ LockActivity观察到事件 
→ 启动UserInfoActivity 
→ 传递ClientLoginData 
→ 退出LockActivity
```

### 退出登录流程
```
用户点击退出登录
→ 显示确认对话框
→ 用户确认
→ UserInfoViewModel.logout
→ UserManager.logout
→ 清除本地数据
→ 通知登录状态监听器
→ 悬浮窗隐藏用户信息按钮
→ 关闭页面
```

### 悬浮窗用户信息按钮流程
```
用户登录成功
→ UserManager.setUserLoggedIn
→ 通知UserLoginStatusListener
→ FloatingWindowService.onLoginStatusChanged
→ 显示用户信息按钮

用户点击悬浮窗用户信息按钮
→ FloatingWindowService.openUserInfoActivity
→ 获取ClientLoginData
→ 启动UserInfoActivity
```

## 🔗 技术实现要点

### MVVM架构
- 使用ViewModel管理UI状态和业务逻辑
- LiveData实现数据绑定和状态观察
- 协程处理异步操作

### 图片加载
- Coil库加载网络头像
- 支持占位图和错误处理
- 圆形裁剪变换
- 完整的加载状态监听

### 状态管理
- UserManager统一管理用户状态
- MMKV持久化存储
- 内存缓存优化

### 用户体验
- 流畅的页面跳转
- 友好的错误处理
- 确认对话框防误操作

### 兼容性处理
- Android 11+ WindowInsetsController API
- Android 13+ getSerializableExtra API
- 向下兼容旧版本Android

### 数据安全
- 输入验证和边界检查
- 异常捕获和处理
- 防止数据溢出显示

## 🚀 使用方式

### 启动用户信息页面
```kotlin
UserInfoActivity.start(context, clientLoginData)
```

### 观察登录跳转事件
```kotlin
loginViewModel.navigateToUserInfo.observe(this) { userData ->
    if (userData != null) {
        navigateToUserInfoActivity(userData)
        loginViewModel.clearNavigateToUserInfo()
    }
}
```

## 📝 注意事项

1. **数据传递**：ClientLoginData必须实现Serializable接口
2. **内存管理**：及时清理LiveData事件，避免重复跳转
3. **错误处理**：网络图片加载失败时显示默认头像
4. **状态同步**：退出登录后确保所有相关状态被清除
5. **UI适配**：支持不同屏幕尺寸和主题
6. **API兼容性**：支持Android 11+的新WindowInsetsController API
7. **数据验证**：用户名长度限制，余额范围检查
8. **异常处理**：完善的try-catch错误处理机制

## ✅ 实现完成状态

### 已完成功能
- ✅ 用户信息页面UI设计和实现
- ✅ 登录成功后直接跳转（移除弹窗）
- ✅ 用户头像加载（支持网络图片和默认头像）
- ✅ 用户信息显示（昵称、余额）
- ✅ 退出登录功能（带确认对话框）
- ✅ MVVM架构实现
- ✅ 错误处理和异常捕获
- ✅ Android版本兼容性处理
- ✅ 数据验证和边界检查

### 代码质量保证
- ✅ 完整的异常处理机制
- ✅ 内存泄漏防护
- ✅ API兼容性处理
- ✅ 用户体验优化
- ✅ 详细的日志记录

## 🔄 后续优化建议

1. **缓存优化**：添加头像本地缓存机制
2. **动画效果**：添加页面切换动画
3. **数据刷新**：支持用户信息实时更新
4. **主题适配**：支持深色模式
5. **无障碍支持**：添加无障碍功能支持
6. **性能监控**：添加页面性能监控
7. **用户行为统计**：添加用户操作统计
