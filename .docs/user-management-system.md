# 用户管理系统文档

## 📋 系统概述

用户管理系统是TimeController应用的核心组件，负责设备UUID生成、用户登录状态管理、用户信息存储等功能。采用双重存储策略确保数据的可靠性和持久性。

## 🏗️ 系统架构

### 核心组件
- **UserManager**: 单例用户管理器，提供统一的用户管理API
- **UUIDv7Generator**: UUIDv7生成器，基于时间戳的唯一ID生成
- **UserInfo**: 用户信息数据类，包含完整的用户状态信息
- **UserStorageKeys**: 存储常量管理，统一管理所有存储键名

### 存储策略
- **主存储**: MMKV高性能键值存储
- **备份存储**: MediaStore API文件存储 (Documents/TimeController/device_uuid.txt)
- **读取优先级**: MMKV → MediaStore文件 → 重新生成

## 🔧 核心API接口

### 设备ID管理
```kotlin
// 获取设备ID（同步）
val deviceId = UserManager.getDeviceId()

// 获取设备ID（异步）
val deviceId = UserManager.getDeviceIdAsync()

// 刷新设备ID（重新生成）
val newDeviceId = UserManager.refreshDeviceId()
```

### 用户登录状态管理
```kotlin
// 检查用户是否已登录
val isLoggedIn = UserManager.isUserLoggedIn()

// 设置用户登录状态
UserManager.setUserLoggedIn(true, "user123", "张三")

// 设置用户登出
UserManager.setUserLoggedIn(false)

// 获取用户登录时间
val loginTime = UserManager.getUserLoginTime()
```

### 用户信息管理
```kotlin
// 获取当前用户信息
val userInfo = UserManager.getUserInfo()

// 保存用户信息
val newUserInfo = UserInfo.createLoggedIn("deviceId", "userId", "userName")
UserManager.saveUserInfo(newUserInfo)

// 清除用户信息
UserManager.clearUserInfo()

// 更新用户活跃时间
UserManager.updateUserActiveTime()
```

### 系统管理
```kotlin
// 初始化UserManager（在Application中调用）
UserManager.initialize(context)

// 清理资源（在Application销毁时调用）
UserManager.cleanup()
```

## 📊 数据结构

### UserInfo数据类
```kotlin
data class UserInfo(
    val userId: String? = null,           // 用户ID
    val userName: String? = null,         // 用户名
    val loginTime: Long? = null,          // 登录时间戳
    val lastActiveTime: Long? = null,     // 最后活跃时间
    val deviceId: String? = null,         // 设备UUID
    val isLoggedIn: Boolean = false,      // 登录状态
    val extraInfo: String? = null         // 扩展信息
)
```

### 业务方法
```kotlin
// 检查登录是否有效（默认24小时过期）
userInfo.isValidLogin()

// 获取登录持续时间
userInfo.getLoginDuration()

// 更新最后活跃时间
userInfo.updateLastActiveTime()

// 更新登录状态
userInfo.updateLoginStatus(true)
```

### 使用的开源库
- **库名**: `com.github.f4b6a3:uuid-creator`
- **版本**: `6.0.0`
- **特点**: 高性能、线程安全、符合RFC标准的UUID生成库
- **支持**: 完整的UUIDv7实现，包括自定义时间戳生成

## 💾 存储机制

### MMKV存储键名
```kotlin
USER_DEVICE_UUID           // 设备UUID
USER_LOGIN_STATUS          // 登录状态
USER_LOGIN_TIME           // 登录时间
USER_LAST_ACTIVE_TIME     // 最后活跃时间
USER_INFO                 // 完整用户信息JSON
USER_ID                   // 用户ID
USER_NAME                 // 用户名
DEVICE_UUID_GENERATED_TIME // UUID生成时间
DEVICE_UUID_BACKUP_STATUS  // 备份状态
```

### MediaStore备份
- **文件路径**: `Documents/TimeController/device_uuid.txt`
- **文件类型**: `text/plain`
- **兼容性**: 支持Android 10+ Scoped Storage和传统存储


### 手动测试步骤
1. **设备ID测试**
   - 首次启动应用，检查设备ID生成
   - 重启应用，验证设备ID一致性
   - 检查MediaStore备份文件是否创建

2. **登录状态测试**
   - 设置登录状态，验证状态持久化
   - 重启应用，检查登录状态是否保持
   - 测试登录过期机制

3. **用户信息测试**
   - 保存用户信息，验证数据完整性
   - 更新用户信息，检查变更是否生效
   - 清除用户信息，确认数据清理

## 🔄 集成现有系统

### 已完成的集成
- ✅ MyApplication中初始化UserManager
- ✅ LoginViewModel使用UserManager获取设备ID
- ✅ PaymentViewModel使用UserManager获取设备ID
- ✅ LockActivity在登录成功时更新用户状态

### 建议的后续集成
1. **CheckWhenLauncherActivity**
   ```kotlin
   // 在应用启动验证时更新用户活跃时间
   UserManager.updateUserActiveTime()
   ```

2. **AppLifecycleAccessibilityService**
   ```kotlin
   // 在应用切换时记录用户活跃状态
   if (UserManager.isUserLoggedIn()) {
       UserManager.updateUserActiveTime()
   }
   ```

3. **CountdownService**
   ```kotlin
   // 在倒计时结束时可以考虑登出用户
   UserManager.setUserLoggedIn(false)
   ```

## ⚠️ 注意事项

### 权限要求
- **存储权限**: MediaStore备份需要适当的存储权限
- **优雅降级**: 如果MediaStore备份失败，系统仍可正常工作

### 性能考虑
- **缓存机制**: 设备ID和用户信息采用内存缓存
- **异步操作**: MediaStore操作在后台线程执行
- **更新频率**: 用户活跃时间更新有5分钟间隔限制

### 安全考虑
- **数据加密**: 敏感信息可考虑加密存储
- **权限控制**: 确保只有授权组件可以修改用户状态
- **数据备份**: 双重存储策略提供数据冗余保护

## 🚀 使用示例

### 基本使用
```kotlin
class MyActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 获取设备ID
        val deviceId = UserManager.getDeviceId()
        
        // 检查登录状态
        if (UserManager.isUserLoggedIn()) {
            // 用户已登录，更新活跃时间
            UserManager.updateUserActiveTime()
            // 继续正常流程
        } else {
            // 用户未登录，跳转到登录页面
            startLoginActivity()
        }
    }
    
    private fun onLoginSuccess(userId: String, userName: String) {
        // 登录成功，更新用户状态
        UserManager.setUserLoggedIn(true, userId, userName)
    }
}
```

### 高级使用
```kotlin
class UserService {
    suspend fun syncUserData() {
        val userInfo = UserManager.getUserInfo()
        if (userInfo?.isValidLogin() == true) {
            // 同步用户数据到服务器
            val deviceId = UserManager.getDeviceIdAsync()
            syncToServer(userInfo, deviceId)
        }
    }
    
    fun handleUserLogout() {
        // 清理用户数据
        UserManager.clearUserInfo()
        // 跳转到登录页面
        navigateToLogin()
    }
}
```

## 📈 后续扩展建议

1. **用户数据同步**: 与服务器同步用户信息
2. **多用户支持**: 支持多个用户账号切换
3. **数据加密**: 敏感信息加密存储
4. **离线模式**: 离线状态下的用户管理
5. **数据分析**: 用户行为数据收集和分析

---

*本文档描述了TimeController应用的用户管理系统实现，包含完整的API接口、使用示例和集成指南。*
