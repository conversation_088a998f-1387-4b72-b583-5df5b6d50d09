# TimeController 项目文档

## 项目概述

TimeController 是一个基于Android设备所有者权限的**应用启动控制和时间管理系统**，通过无障碍服务实现应用启动拦截、付款验证、权限自动管理等功能。

### 🎯 核心功能
- **应用启动控制**：拦截受控应用启动，进行权限验证
- **付款验证系统**：WebSocket实时通信，支持小程序码付款验证
- **时间管理**：倒计时控制和悬浮窗显示
- **权限自动管理**：设备所有者权限下的智能权限获取
- **保活机制**：多服务互相保活，确保系统稳定运行

## 文档结构

### 📋 项目规划
- [AI开发指南](PROJECT-TODOLIST.md) - 完整的开发任务清单和实现边界
- [系统验证报告](SYSTEM-VERIFICATION-REPORT.md) - 最新的功能验证和架构分析

### 📁 architecture/ - 架构设计
- 系统架构设计文档
- 组件关系图和数据流设计
- 技术栈和状态管理

### 📁 features/ - 功能特性
- 应用启动控制和监控
- 无障碍服务和悬浮窗功能
- 保活机制和自动启动
- 各功能模块的详细说明

### 📁 development/ - 开发相关
- 代码重构记录
- 集成指南和开发文档

### 📁 permissions/ - 权限管理
- 权限获取流程和管理工具
- 智能权限初始化

## 快速导航

### 📋 项目规划
- [AI开发指南](PROJECT-TODOLIST.md) - 开发任务和实现边界
- [系统验证报告](SYSTEM-VERIFICATION-REPORT.md) - 功能验证和架构分析

### 🏗️ 架构设计
- [系统架构概览](architecture/system-overview.md) - 完整的系统架构和技术栈

### ⚙️ 功能特性
- [应用监控功能](features/app-monitoring.md) - 应用启动拦截和监控
- [无障碍服务](features/accessibility-service.md) - 核心无障碍服务功能
- [悬浮窗服务](features/floating-window.md) - 全局悬浮窗显示
- [保活机制](features/mutual-keep-alive.md) - 多服务互相保活
- [自动启动和安全](features/auto-startup-and-security.md) - 开机自启动和安全防护

### 🔐 权限管理
- [悬浮窗权限自动获取](permissions/overlay-permission-guide.md) - 权限获取指南
- [智能权限初始化](permissions/smart-permission-init.md) - 权限初始化流程

### 💻 开发指南
- [代码重构记录](development/overlay-permission-refactor.md) - 权限功能重构
- [集成指南](development/integration-guide.md) - 权限助手集成

## 技术栈

### 🔧 核心技术
- **Android架构**：MVVM模式，ViewModel + LiveData
- **网络通信**：Ktor Client + WebSocket实时通信
- **图片加载**：Coil库，支持Base64图片加载
- **数据存储**：MMKV高性能键值存储
- **日志系统**：XLog.构化日志
- **权限管理**：Device Owner API + Accessibility Service

### 🏗️ 系统架构
- **应用启动控制**：CheckService + AIDL接口 + 无障碍服务拦截
- **付款验证**：LockActivity + LoginViewModel + HTTP长轮询
- **应用监控**：AppLifecycleAccessibilityService + 启动拦截机制
- **保活系统**：MutualKeepAliveManager + 多服务协作
- **权限自动化**：DeviceOwnerPermissionManager + AccessibilityUtils

## 更新日志

### 2025-07-03
- ✅ 完成应用启动控制系统核心功能实现
- ✅ 实现CheckWhenLauncherActivity透明验证界面（MVVM架构）
- ✅ 实现LockActivity付款验证界面和PaymentViewModel
- ✅ 集成Ktor Client和WebSocket实时通信
- ✅ 完成应用启动拦截机制和无障碍服务集成
- ✅ 实现Base64小程序码显示和Coil图片加载
- ✅ 完成系统验证报告，功能完整度95%以上

### 2025-07-01
- 完成悬浮窗权限自动获取功能重构
- 添加智能权限初始化功能
- 整理项目文档结构并迁移到 `.docs` 文件夹
- 创建系统架构文档
- 完善功能特性说明文档

## 贡献指南

1. 所有文档使用 Markdown 格式
2. 代码示例需要包含注释和实际实现匹配
3. 更新功能时同步更新相关文档
4. 保持文档的时效性和准确性
5. 新功能开发参考 [AI开发指南](PROJECT-TODOLIST.md)
6. 架构变更需要更新 [系统架构概览](architecture/system-overview.md)
