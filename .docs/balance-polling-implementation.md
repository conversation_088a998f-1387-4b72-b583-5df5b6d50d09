# 余额轮询和倒计时同步机制实现文档

## 概述

本文档记录了用户登录后的余额轮询服务和倒计时同步机制的完整实现，包括全局余额查询长轮询服务、手动刷新功能、以及与CountdownManager的集成。

## 实现功能

### 核心功能
1. **全局余额轮询服务** - 用户登录后每60秒自动查询余额
2. **倒计时同步机制** - 将API返回的`temp_time`同步到CountdownManager
3. **手动刷新功能** - 用户信息页面支持手动刷新余额
4. **生命周期管理** - 与用户登录状态同步启动/停止服务
5. **错误处理机制** - 完善的网络异常和API失败处理

### 技术特性
- 基于现有UserManager + CountdownManager架构
- 使用Ktor Client进行网络请求
- StateFlow状态管理
- 协程异步处理
- 自动重试机制

## 文件修改清单

### 1. 网络层扩展

#### NetworkConfig.kt
```kotlin
// 新增查询用户余额API端点
const val ENDPOINT_QUERY_BALANCE = "v1/checkUserMoney"
```

#### ApiResponse.kt
```kotlin
// ClientLoginData添加temp_time字段
data class ClientLoginData(
    // ... 其他字段
    val temp_time: Int? = null // 剩余时长（秒）
)

// 新增查询余额响应数据结构
@Serializable
data class QueryBalanceResponse(
    val code: Int,
    val msg: String,
    @Serializable(with = FlexibleClientLoginDataSerializer::class)
    val rspdata: ClientLoginData? = null,
    val rsptime: Long
)
```

### 2. 业务逻辑层

#### UserLoginStatusListener.kt (新增)
```kotlin
interface UserLoginStatusListener {
    /**
     * 用户登录状态发生变化
     * @param isLoggedIn 是否已登录
     */
    fun onLoginStatusChanged(isLoggedIn: Boolean)
}
```

#### BalancePollingManager.kt (新增)
```kotlin
object BalancePollingManager : UserLoginStatusListener {
    // 轮询间隔60秒
    private const val POLLING_INTERVAL_MS = 60_000L

    // 核心功能
    - startPolling() // 启动轮询
    - stopPolling() // 停止轮询
    - queryBalance() // 执行余额查询
    - refreshBalance() // 手动刷新
    - handleBalanceResponse() // 处理响应

    // 状态管理
    - isPollingActive: StateFlow<Boolean>
    - lastBalanceData: StateFlow<ClientLoginData?>
    - pollingError: StateFlow<String?>
}
```

#### UserManager.kt
```kotlin
// 集成BalancePollingManager
fun initialize(context: Context) {
    // ... 现有逻辑
    BalancePollingManager.initialize()
}

fun cleanup() {
    BalancePollingManager.cleanup()
    // ... 现有逻辑
}
```

### 3. 用户界面层

#### UserInfoViewModel.kt
```kotlin
// 新增余额刷新功能
private val _isRefreshingBalance = MutableLiveData<Boolean>()
val isRefreshingBalance: LiveData<Boolean> = _isRefreshingBalance

fun refreshBalance() {
    // 调用BalancePollingManager.refreshBalance()
    // 更新UI状态
}
```

#### UserInfoActivity.kt
```kotlin
// 新增刷新按钮点击处理
binding.ivRefreshBalance.setOnClickListener {
    viewModel.refreshBalance()
}

// 观察刷新状态
viewModel.isRefreshingBalance.observe(this) { isRefreshing ->
    // 更新按钮状态和动画
}
```

#### activity_user_info.xml
```xml
<!-- 在余额显示右侧添加刷新按钮 -->
<ImageView
    android:id="@+id/iv_refresh_balance"
    android:layout_width="24dp"
    android:layout_height="24dp"
    android:src="@drawable/ic_refresh"
    android:layout_marginStart="8dp"
    android:background="?attr/selectableItemBackgroundBorderless"
    android:contentDescription="刷新余额" />
```

### 4. 数据模型更新

#### model/UserInfo.kt
```kotlin
data class UserInfo(
    // ... 其他字段
    val temp_time: Int? = null, // 剩余时长（秒）
)

// 更新相关方法以支持temp_time字段
```

### 5. 资源文件

#### ic_refresh.xml (新增)
- 刷新图标的矢量drawable资源

## API集成说明

### 查询用户余额接口
- **端点**: `GET /{version}/checkUserMoney`
- **参数**: `userId` - 用户ID
- **响应**: 标准JsonResponse格式，rspdata包含用户余额和temp_time

### 响应数据结构

**实际API响应格式**：
```json
{
  "code": 200,
  "msg": "请求成功",
  "rspdata": 40,
  "jwt": "",
  "rsptime": 1752241228
}
```

**数据处理流程**：
1. **API响应**：`rspdata` 字段直接返回数字 `40`，表示用户余额
2. **序列化层**：自动转换为 `ClientLoginData(money=40)`
3. **业务逻辑层**：计算 `temp_time = 40 * 10 = 400` 秒
4. **最终数据**：`ClientLoginData(money=40, temp_time=400)`

## 工作流程

### 1. 服务启动流程
```
用户登录成功 → UserManager.setUserLoggedIn(true) 
→ 通知BalancePollingManager → 启动60秒轮询服务
```

### 2. 轮询执行流程
```
定时器触发 → 调用checkUserMoney API → 解析响应数据 
→ 更新UserManager中的ClientLoginData → 同步temp_time到CountdownManager
```

### 3. 手动刷新流程
```
用户点击刷新按钮 → UserInfoViewModel.refreshBalance() 
→ BalancePollingManager.refreshBalance() → 立即执行API调用 → 更新UI
```

### 4. 服务停止流程
```
用户退出登录 → UserManager.logout() → 通知BalancePollingManager 
→ 停止轮询服务 → 清除余额数据
```

## 倒计时同步机制

### 同步逻辑
```kotlin
// 在handleBalanceResponse中
balanceData.temp_time?.let { tempTimeSeconds ->
    if (tempTimeSeconds > 0) {
        val tempTimeMillis = tempTimeSeconds * 1000L
        CountdownManager.updateRemainingTime(tempTimeMillis)
    }
}
```

### 临时实现
- 服务器端：`temp_time = money * 10`（秒）
- 客户端：将秒转换为毫秒后更新CountdownManager

## 错误处理

### 网络异常处理
- 自动重试机制（出错后10秒重试）
- 错误状态通过StateFlow暴露给UI
- 日志记录所有异常情况

### 业务异常处理
- 用户ID为空检查
- API响应数据验证
- 登录状态检查

### API数据格式适配

#### 架构设计原则
- **序列化层**：只负责数据类型转换，保持数据原始性
- **业务逻辑层**：负责数据的业务逻辑处理和计算

#### 实现方案
- **问题**：API返回的 `rspdata` 可能是对象、数字、字符串等不同类型
- **解决方案**：实现通用的 `FlexibleSerializer<T>` 泛型序列化器
- **核心特性**：
  - 支持对象、数组、基础类型的自动适配
  - 序列化器只做类型转换，不做业务逻辑
  - 完全类型安全，支持任意数据类型

#### 数据转换流程
1. **序列化层转换**：
   - 数字 `40` → `ClientLoginData(money=40)`
   - 字符串数字 `"40"` → `ClientLoginData(money=40)`
   - 普通字符串 → `ClientLoginData(userName=字符串)`
   - 对象 → 正常反序列化

2. **业务逻辑层处理**：
   - 在 `BalancePollingManager` 中计算 `temp_time = money * 10`
   - 创建完整的业务数据对象
   - 同步到 CountdownManager

## 性能优化

### 资源管理
- 使用SupervisorJob避免子协程异常影响父协程
- 正确的协程作用域管理
- 及时取消不需要的协程任务

### 网络优化
- 复用现有的Ktor Client
- 合理的超时配置
- 错误重试间隔控制

## 测试建议

### 功能测试
1. 用户登录后验证轮询服务启动
2. 手动刷新功能测试
3. 用户退出登录后验证服务停止
4. 网络异常情况测试
5. 倒计时同步验证

### 性能测试
1. 长时间运行稳定性测试
2. 内存泄漏检查
3. 网络请求频率验证

## 通用序列化器使用指南

### FlexibleSerializer<T> 泛型序列化器
这是一个通用的序列化器，可以处理API响应中数据类型不一致的问题：

```kotlin
// 创建自定义序列化器
object FlexibleUserSerializer : KSerializer<User?> by FlexibleSerializer(
    targetSerializer = User.serializer(),
    primitiveHandler = { element ->
        when {
            element.isString -> User(name = element.content)
            !element.isString -> element.intOrNull?.let { User(id = it) }
            else -> null
        }
    }
)

// 在数据类中使用
@Serializable
data class ApiResponse(
    @Serializable(with = FlexibleUserSerializer::class)
    val userData: User? = null
)
```

### 支持的数据类型转换
- **JsonObject** → 正常反序列化为目标类型
- **JsonArray** → 尝试反序列化（如果目标类型支持）
- **JsonPrimitive** → 使用自定义处理器转换
- **JsonNull** → 返回null

## 后续扩展

### 可能的优化方向
1. 支持动态调整轮询间隔
2. 添加网络状态监听
3. 支持推送通知替代轮询
4. 添加数据缓存机制
5. 支持离线模式
6. 扩展FlexibleSerializer支持更多复杂场景

---

*本文档记录了TimeController应用余额轮询和倒计时同步机制的完整实现，包含所有相关代码修改和集成说明。*
