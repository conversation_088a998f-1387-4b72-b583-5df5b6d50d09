{"swagger": "2.0", "info": {"description": "接口文档", "version": "1.0.0", "title": "剧本杀", "termsOfService": "-", "contact": {"name": "huan<PERSON><PERSON>"}, "license": {"name": "智楹科技", "url": "http://www.zhiying2023.cn/"}}, "host": "game.3ddu.cn", "basePath": "/estate", "tags": [{"name": "juben-user-controller", "description": "用户控制器"}], "paths": {"/{version}/LoginXCX": {"post": {"tags": ["juben-user-controller"], "summary": "小程序登录", "description": "小程序登录", "operationId": "clientLogin2UsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "jwt", "in": "header", "description": "令牌", "required": true, "type": "string"}, {"name": "openId", "in": "query", "description": "openId", "required": false, "type": "string"}, {"name": "secret<PERSON>ey", "in": "query", "description": "secret<PERSON>ey", "required": false, "type": "string"}, {"name": "version", "in": "path", "description": "版本(v1、v2)", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResponse"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/{version}/checkUserMoney": {"get": {"tags": ["juben-user-controller"], "summary": "查询用户余额", "description": "查询用户余额", "operationId": "checkUserUsingGET", "produces": ["*/*"], "parameters": [{"name": "userId", "in": "query", "description": "userId", "required": false, "type": "string"}, {"name": "version", "in": "path", "description": "版本(v1、v2)", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResponse"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/{version}/clientLogin": {"get": {"tags": ["juben-user-controller"], "summary": "客户端登录", "description": "客户端登录", "operationId": "clientLoginUsingGET", "produces": ["*/*"], "parameters": [{"name": "secret<PERSON>ey", "in": "query", "description": "secret<PERSON>ey", "required": false, "type": "string"}, {"name": "version", "in": "path", "description": "版本(v1、v2)", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResponse"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/{version}/datiUser": {"get": {"tags": ["juben-user-controller"], "summary": "小程序用户", "description": "小程序用户", "operationId": "datiUserUsingGET", "produces": ["*/*"], "parameters": [{"name": "openid", "in": "query", "description": "openid", "required": false, "type": "string"}, {"name": "version", "in": "path", "description": "版本(v1、v2)", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResponse"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/{version}/get-openid": {"get": {"tags": ["juben-user-controller"], "summary": "获取OpenID", "description": "获取OpenID", "operationId": "GetOpenIdUsingGET", "produces": ["*/*"], "parameters": [{"name": "code", "in": "query", "description": "登录凭证", "required": true, "type": "string"}, {"name": "version", "in": "path", "description": "版本(v1、v2)", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResponse"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/{version}/orderList": {"get": {"tags": ["juben-user-controller"], "summary": "订单列表", "description": "订单列表", "operationId": "addYanChuUsingGET", "produces": ["*/*"], "parameters": [{"name": "jwt", "in": "header", "description": "令牌", "required": true, "type": "string"}, {"name": "userId", "in": "query", "description": "userId", "required": false, "type": "string"}, {"name": "version", "in": "path", "description": "版本(v1、v2)", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResponse"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}, "/{version}/recharge": {"post": {"tags": ["juben-user-controller"], "summary": "充值", "description": "充值", "operationId": "ticketListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "jwt", "in": "header", "description": "令牌", "required": true, "type": "string"}, {"name": "money", "in": "query", "description": "money", "required": false, "type": "integer", "format": "int32"}, {"name": "openId", "in": "query", "description": "openId", "required": false, "type": "string"}, {"name": "version", "in": "path", "description": "版本(v1、v2)", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/JsonResponse"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "definitions": {"JsonResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "jwt": {"type": "string"}, "msg": {"type": "string"}, "rspdata": {"type": "object"}, "rsptime": {"type": "integer", "format": "int64"}}, "title": "JsonResponse"}}}