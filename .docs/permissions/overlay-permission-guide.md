# 悬浮窗权限自动获取指南

## 概述
本指南介绍如何使用精简版的 `OverlayPermissionHelper` 自动获取悬浮窗权限。

## 核心特性

### 🎯 主要功能
- **自动权限检查**：启动时检查是否已有悬浮窗权限
- **智能页面导航**：自动打开设置页面并导航到权限设置
- **自动操作**：自动点击开关或按钮开启权限
- **状态监控**：实时监控权限获取过程
- **错误处理**：完善的异常处理和重试机制

### 📋 支持的页面类型
1. **应用信息页面**：包含各种权限选项的主页面
2. **悬浮窗权限设置页面**：具体的权限开关页面
3. **未知页面**：其他不相关的页面

## 使用方法

### 基本用法
```kotlin
class YourAccessibilityService : AccessibilityService() {

    private var overlayPermissionHelper: OverlayPermissionHelper? = null

    override fun onServiceConnected() {
        super.onServiceConnected()

        // 初始化权限助手
        overlayPermissionHelper = OverlayPermissionHelper(this)
        
        // 自动获取悬浮窗权限
        overlayPermissionHelper?.autoGrantOverlayPermission(
            onGranted = {
                // 权限获取成功
                Toast.makeText(this, "悬浮窗权限已获取", Toast.LENGTH_SHORT).show()
                // 启动悬浮窗服务
                FloatingWindowService.start(this)
            },
            onFailed = {
                // 权限获取失败
                Toast.makeText(this, "悬浮窗权限获取失败", Toast.LENGTH_SHORT).show()
            }
        )
    }
    
    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        if (event == null) return
        
        // 让助手处理事件
        overlayPermissionHelper?.onAccessibilityEvent(event)
        
        // 其他事件处理逻辑...
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理资源
        overlayPermissionHelper?.cleanup()
        overlayPermissionHelper = null
    }
}
```

### 高级配置
```kotlin
// 检查权限状态
private fun checkPermissionStatus() {
    val hasPermission = Settings.canDrawOverlays(this)
    if (hasPermission) {
        // 已有权限，直接启动服务
        FloatingWindowService.start(this)
    } else {
        // 没有权限，启动自动获取流程
        overlayPermissionHelper?.autoGrantOverlayPermission(onGranted, onFailed)
    }
}
```

## 工作流程

### 1. 权限检查阶段
```
启动 → 检查悬浮窗权限 → 已有权限？
                        ├─ 是 → 直接调用成功回调
                        └─ 否 → 进入自动获取流程
```

### 2. 自动获取阶段
```
打开应用信息页面 → 页面状态检测 → 点击悬浮窗权限项 → 进入权限设置页面
                                                    ↓
返回应用 ← 权限获取成功 ← 检查权限状态 ← 点击开关/按钮
```

### 3. 状态监控
- **实时页面检测**：识别当前所在的设置页面
- **自动操作执行**：根据页面状态执行相应操作
- **权限状态验证**：确认权限是否成功获取
- **超时处理**：避免无限等待

## 页面适配

### 支持的关键词
```kotlin
// 悬浮窗相关关键词
val overlayKeywords = listOf(
    "悬浮窗", "浮窗", "overlay", "在其他应用上层显示",
    "显示在其他应用的上层", "Draw over other apps",
    "Display over other apps", "Appear on top"
)

// 允许按钮关键词
val allowKeywords = listOf(
    "允许", "开启", "启用", "Allow", "Enable", "Turn on"
)
```

### 厂商适配
- **原生Android**：完全支持
- **小米MIUI**：支持
- **华为EMUI**：支持
- **OPPO ColorOS**：支持
- **vivo FuntouchOS**：支持
- **三星OneUI**：支持

## 配置参数

### 重试设置
```kotlin
companion object {
    private const val MAX_RETRY_COUNT = 8        // 最大重试次数
    private const val RETRY_DELAY_MS = 1500L     // 重试间隔(毫秒)
}
```

### 超时设置
- **总超时时间**：约12秒 (8次重试 × 1.5秒)
- **页面加载等待**：2秒
- **操作后等待**：1-2秒

## 错误处理

### 常见错误及解决方案

#### 1. 权限获取超时
**原因**：页面加载慢或无法识别页面
**解决**：增加重试次数或调整等待时间

#### 2. 无法找到权限项
**原因**：厂商定制导致页面结构不同
**解决**：添加更多关键词或适配特定厂商

#### 3. 点击操作失败
**原因**：节点不可点击或权限不足
**解决**：尝试点击父节点或使用其他操作方式

### 调试技巧
```kotlin
// 启用详细日志
XLog.d("当前页面状态: $pageState")
XLog.d("找到的节点: ${node.text}")
XLog.d("权限状态: ${hasOverlayPermission()}")
```

## 最佳实践

### 1. 权限检查时机
- 在无障碍服务连接时检查
- 在应用启动时预检查
- 在需要使用悬浮窗前检查

### 2. 用户体验优化
- 提供清晰的状态提示
- 避免频繁的权限请求
- 处理用户拒绝的情况

### 3. 错误处理
- 捕获所有可能的异常
- 提供友好的错误提示
- 记录详细的错误日志

## 注意事项

### 权限要求
- 需要设备所有者权限 (Device Owner)
- 需要无障碍服务权限
- 需要在 AndroidManifest.xml 中声明 SYSTEM_ALERT_WINDOW 权限

### 兼容性
- 适用于 Android 6.0 (API 23) 及以上版本
- 不同厂商的设置页面可能有差异
- 建议在多种设备上测试

### 使用限制
- 同一时间只能有一个权限获取请求在进行
- 如果权限已存在，会直接调用成功回调
- 超时时间约为 12 秒

## 故障排除

### 问题诊断步骤
1. 检查设备所有者权限
2. 确认无障碍服务已启用
3. 查看日志输出
4. 验证页面关键词匹配
5. 测试手动操作流程

### 常见问题
- **Q**: 为什么权限获取失败？
- **A**: 检查设备所有者权限和无障碍服务状态

- **Q**: 如何适配新的厂商？
- **A**: 添加对应的关键词和页面检测逻辑

- **Q**: 可以自定义超时时间吗？
- **A**: 可以修改 `MAX_RETRY_COUNT` 和 `RETRY_DELAY_MS` 常量
