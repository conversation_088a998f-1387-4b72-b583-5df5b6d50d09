# 智能悬浮窗权限初始化功能

## 功能概述
在应用启动时，根据当前的权限状态智能决定悬浮窗的启动策略，提供更好的用户体验。

## 实现逻辑

### 判断流程
```
应用启动
    ↓
检查设备所有者权限
    ↓
检查悬浮窗权限和无障碍权限状态
    ↓
根据权限状态选择启动策略
```

### 三种情况处理

#### 情况1：已有悬浮窗权限
```kotlin
hasOverlayPermission -> {
    // 直接启动悬浮窗服务
    XLog.d("已有悬浮窗权限，直接启动悬浮窗服务")
    FloatingWindowService.start(this)
}
```
**说明**：用户已经授予了悬浮窗权限，无需任何额外操作，直接显示悬浮窗。

#### 情况2：有无障碍权限但没有悬浮窗权限
```kotlin
hasAccessibilityPermission -> {
    // 等待无障碍服务自动获取悬浮窗权限
    XLog.d("有无障碍权限但没有悬浮窗权限，等待无障碍服务自动获取悬浮窗权限")
    // 无障碍服务启动后会自动处理悬浮窗权限获取
}
```
**说明**：无障碍服务已启用，它会在启动时自动获取悬浮窗权限并显示悬浮窗。

#### 情况3：都没有权限
```kotlin
else -> {
    // 延用目前的流程
    XLog.d("无障碍权限和悬浮窗权限都没有，延用目前的流程")
    // 用户需要通过MainActivity手动启用无障碍服务
}
```
**说明**：保持原有的用户引导流程，通过MainActivity引导用户启用无障碍服务。

## 代码实现

### 核心方法
```kotlin
/**
 * 智能初始化悬浮窗
 */
private fun smartInitFloatingWindow() {
    try {
        // 检查设备所有者权限
        if (!isDeviceOwner()) {
            XLog.w("没有设备所有者权限，跳过悬浮窗初始化")
            return
        }

        val hasOverlayPermission = checkOverlayPermission()
        val hasAccessibilityPermission = checkAccessibilityPermission()
        
        XLog.d("权限状态检查 - 悬浮窗权限: $hasOverlayPermission, 无障碍权限: $hasAccessibilityPermission")

        when {
            hasOverlayPermission -> {
                // 情况1: 直接显示悬浮窗
                FloatingWindowService.start(this)
            }
            hasAccessibilityPermission -> {
                // 情况2: 等待无障碍服务自动获取悬浮窗权限
                // 无障碍服务会自动处理
            }
            else -> {
                // 情况3: 延用目前的流程
                // 通过MainActivity引导用户
            }
        }
    } catch (e: Exception) {
        XLog.e("智能初始化悬浮窗失败", e)
    }
}
```

### 权限检查方法
```kotlin
/**
 * 检查悬浮窗权限
 */
private fun checkOverlayPermission(): Boolean {
    return Settings.canDrawOverlays(this)
}

/**
 * 检查无障碍权限
 */
private fun checkAccessibilityPermission(): Boolean {
    return try {
        val accessibilityUtils = AccessibilityUtils(this)
        accessibilityUtils.isAccessibilityServiceEnabled()
    } catch (e: Exception) {
        XLog.e("检查无障碍权限失败", e)
        false
    }
}
```

## 用户体验改进

### 改进前
- 用户每次启动应用都需要手动操作
- 即使已有权限也不会自动显示悬浮窗
- 权限获取流程不够智能

### 改进后
- **自动化程度更高**：根据权限状态自动选择最佳策略
- **用户体验更好**：已有权限时立即显示悬浮窗
- **流程更智能**：避免不必要的权限请求

## 日志输出示例

### 情况1：已有悬浮窗权限
```
D/MyApplication: 权限状态检查 - 悬浮窗权限: true, 无障碍权限: true
D/MyApplication: 已有悬浮窗权限，直接启动悬浮窗服务
D/FloatingWindowService: FloatingWindowService onCreate
```

### 情况2：只有无障碍权限
```
D/MyApplication: 权限状态检查 - 悬浮窗权限: false, 无障碍权限: true
D/MyApplication: 有无障碍权限但没有悬浮窗权限，等待无障碍服务自动获取悬浮窗权限
D/AppLifecycleAccessibilityService: 无障碍服务已连接
D/SimpleOverlayHelper: 开始自动获取悬浮窗权限
```

### 情况3：都没有权限
```
D/MyApplication: 权限状态检查 - 悬浮窗权限: false, 无障碍权限: false
D/MyApplication: 无障碍权限和悬浮窗权限都没有，延用目前的流程
```

## 兼容性说明

- **向后兼容**：保留了原有的 `initFloatingWindow()` 方法作为备用
- **渐进式改进**：不影响现有的权限获取流程
- **错误处理**：所有异常都被捕获，不会影响应用启动

## 测试建议

### 测试场景
1. **全新安装**：测试无任何权限时的行为
2. **只有无障碍权限**：测试自动获取悬浮窗权限的流程
3. **已有悬浮窗权限**：测试直接显示悬浮窗的效果
4. **权限被撤销**：测试权限状态变化时的处理

### 验证要点
- 权限检查的准确性
- 不同情况下的启动策略
- 日志输出的完整性
- 异常情况的处理

## 总结

通过这个智能初始化功能：
- ✅ **提升用户体验**：减少不必要的手动操作
- ✅ **提高自动化程度**：根据权限状态智能选择策略
- ✅ **保持兼容性**：不影响现有功能
- ✅ **增强稳定性**：完善的异常处理机制

这个改进让应用在启动时能够更智能地处理悬浮窗的显示，为用户提供更流畅的使用体验。
