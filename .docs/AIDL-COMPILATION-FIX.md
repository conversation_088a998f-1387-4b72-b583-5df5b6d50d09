# AIDL编译修复指南

## 问题说明

CheckService中找不到ICheckService接口，这是因为AIDL文件需要先编译才能生成对应的Java接口类。

## 修复步骤

### 1. 编译项目
首先编译项目以生成AIDL接口：
```bash
./gradlew build
```

### 2. 修复CheckService.kt
编译成功后，取消注释CheckService.kt中的以下代码：

#### 在onBind方法中：
```kotlin
override fun onBind(intent: Intent?): IBinder? {
    XLog.d("CheckService绑定")
    return checkServiceBinder  // 取消注释这行
    // return null              // 注释掉这行
}
```

#### 取消注释AIDL服务实现：
```kotlin
/**
 * AIDL服务实现
 */
private val checkServiceBinder = object : ICheckService.Stub() {
    
    override fun check(need_show_lock: Boolean): <PERSON><PERSON><PERSON> {
        return performCheck(need_show_lock)
    }
}
```

### 3. 完整的修复代码

将CheckService.kt中第109-114行替换为：
```kotlin
override fun onBind(intent: Intent?): IBinder {
    XLog.d("CheckService绑定")
    return checkServiceBinder
}
```

将CheckService.kt中第124-136行替换为：
```kotlin
/**
 * AIDL服务实现
 */
private val checkServiceBinder = object : ICheckService.Stub() {
    
    override fun check(need_show_lock: Boolean): Boolean {
        return performCheck(need_show_lock)
    }
}
```

### 4. 验证修复
编译完成后，CheckService应该能够正常工作：
- ICheckService接口会自动生成
- CheckService可以正常绑定和提供AIDL服务
- Unity应用可以通过AIDL接口调用check()方法

## 注意事项

1. **编译顺序很重要**：必须先编译AIDL文件生成接口，然后才能在Kotlin代码中使用
2. **包名一致性**：确保AIDL文件中的package声明与项目包名一致
3. **清理重建**：如果遇到问题，可以执行`./gradlew clean build`重新编译

## 验证方法

编译成功后，可以在以下位置找到生成的接口文件：
```
app/build/generated/aidl_source_output_dir/debug/out/com/hwb/timecontroller/service/ICheckService.java
```

该文件包含了AIDL编译生成的Java接口定义。
