# 隐藏未登录用户的充值提醒通知

## 问题描述

用户反馈：如果用户未登录，不需要显示倒计时快结束的相关通知（充值提醒）。

## 问题分析

在原有实现中，悬浮窗的充值提醒逻辑只检查倒计时状态和剩余时间，没有考虑用户登录状态：

```kotlin
val shouldShowRechargeNotification =
    data.state == CountdownManager.CountdownState.RUNNING &&
            data.remainingTimeMillis <= 5 * 60 * 1000L // 5分钟 = 300000毫秒
```

这导致即使用户未登录，当倒计时≤5分钟时仍会显示充值提醒，包括：
- 充值提醒文本
- 颜色渐变动画
- 脉动动画效果

## 解决方案

在充值提醒的判断条件中添加用户登录状态检查，只有用户已登录时才显示充值相关通知。

### 修改逻辑

**修改前：**
```kotlin
// 检查是否需要显示充值提醒（倒数5分钟开始）
val shouldShowRechargeNotification =
    data.state == CountdownManager.CountdownState.RUNNING &&
            data.remainingTimeMillis <= 5 * 60 * 1000L // 5分钟 = 300000毫秒
```

**修改后：**
```kotlin
// 检查是否需要显示充值提醒（倒数5分钟开始，且用户已登录）
val shouldShowRechargeNotification =
    data.state == CountdownManager.CountdownState.RUNNING &&
            data.remainingTimeMillis <= 5 * 60 * 1000L && // 5分钟 = 300000毫秒
            UserManager.isUserLoggedIn() // 用户必须已登录
```

## 修改文件

### FloatingWindowService.kt

**文件路径：** `app/src/main/java/com/hwb/timecontroller/service/FloatingWindowService.kt`

**修改方法：** `updateCountdownDisplay(data: CountdownManager.CountdownData)`

**修改行数：** 411-415行

## 功能效果

### 用户已登录时

**倒计时 > 5分钟：**
- 显示正常倒计时文本（如 "10:30"）
- 白色文字颜色
- 无特殊动画效果

**倒计时 ≤ 5分钟：**
- 显示充值提醒文本（如 "05:30 请及时充值"）
- 启动颜色渐变动画（红色渐变）
- 启动脉动动画效果
- 根据剩余时间调整脉动频率

### 用户未登录时

**任何倒计时状态：**
- 只显示正常倒计时文本
- 根据状态显示相应颜色（运行中：白色，结束：红色）
- 不显示充值提醒文本
- 不启动任何充值相关动画效果

## 技术实现

### 1. 条件判断增强

在原有的两个条件基础上，增加第三个条件：
1. `data.state == CountdownManager.CountdownState.RUNNING` - 倒计时运行中
2. `data.remainingTimeMillis <= 5 * 60 * 1000L` - 剩余时间≤5分钟
3. `UserManager.isUserLoggedIn()` - 用户已登录

### 2. 逻辑分支

```kotlin
if (shouldShowRechargeNotification) {
    // 显示充值提醒文本
    tvCountdown?.text = "$timeText ${getString(R.string.recharge_reminder)}"
    
    // 启动充值提醒效果
    startRechargeNotificationEffects(data.remainingTimeMillis)
} else {
    // 正常显示倒计时
    tvCountdown?.text = timeText
    
    // 停止充值提醒效果
    stopRechargeNotificationEffects()
    
    // 根据状态改变颜色（正常状态）
    val textColor = when (data.state) {
        CountdownManager.CountdownState.RUNNING -> getColor(android.R.color.white)
        CountdownManager.CountdownState.FINISHED -> getColor(android.R.color.holo_red_light)
    }
    tvCountdown?.setTextColor(textColor)
}
```

## 用户体验改进

### 1. 避免混淆

未登录用户不会看到充值相关提醒，避免产生困惑：
- 不知道如何充值
- 不理解充值提醒的含义
- 误以为需要付费才能使用

### 2. 界面简洁

未登录状态下保持界面简洁：
- 只显示必要的倒计时信息
- 无多余的动画效果
- 减少视觉干扰

### 3. 逻辑一致

与整体应用逻辑保持一致：
- 充值功能需要登录后才能使用
- 充值提醒也应该只对已登录用户显示

## 测试场景

### 1. 未登录用户
- 启动应用，不登录
- 手动设置倒计时（如3分钟）
- 观察悬浮窗显示：应该只显示正常倒计时，无充值提醒

### 2. 已登录用户
- 登录用户账号
- 等待倒计时≤5分钟
- 观察悬浮窗显示：应该显示充值提醒和动画效果

### 3. 登录状态切换
- 在倒计时≤5分钟时退出登录
- 观察悬浮窗变化：充值提醒应该立即消失
- 重新登录：充值提醒应该重新出现

## 相关功能

此修改不影响其他功能：
- 倒计时正常运行和显示
- 倒计时结束后的锁定流程
- 悬浮窗的其他交互功能
- 用户信息显示和管理

---

**修改日期：** 2025-07-11  
**修改人员：** Augment Agent  
**问题状态：** 已修复，待测试验证
