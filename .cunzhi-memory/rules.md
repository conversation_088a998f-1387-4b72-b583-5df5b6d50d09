# 开发规范和规则

- LockActivity正常只有在用户未登录的时候才会进入，不需要区分入口处理用户登录状态和余额情况
- 区分全局计费倒计时和页面内倒计时：全局计费倒计时结束会进入UserInfoActivity，LoginActivity的页面倒计时结束后只需关闭页面，在UserInfoActivity中余额不足时显示提示文案
- CheckService.performCheck的检查逻辑简化：不需要调用接口检查余额，只需要判断用户登录状态，有登录就跳到UserInfoActivity，没登录就跳到LoginActivity
- 修复了全局计费倒计时同步问题：1)将定时同步余额间隔从10分钟调整为5分钟；2)修复了余额同步后倒计时更新逻辑，当倒计时正在运行时直接更新CountdownManager剩余时间而不是启动新的CountdownService，避免多个服务冲突导致倒计时更新失败
- 采用保守方案修复倒计时机制关键问题：1)在CountdownService.onStartCommand中添加重复启动检查，取消已有倒计时再启动新的；2)在observeCountdownState中添加外部时间更新检测，当时间差超过2秒时重新同步倒计时；3)添加restartCountdownTimer方法支持动态更新倒计时时间；4)保持现有lockTaskMode和悬浮窗显示逻辑不变，降低风险
- 倒计时机制修复已完成并确认：1)智能启动检查避免重复启动相同时间的倒计时；2)外部时间更新检测机制通过lastKnownRemainingTime跟踪，时间增加超过30秒时重新同步；3)restartCountdownTimer方法保持暂停状态和扣款进度；4)所有关键操作都有日志输出便于调试；5)定时同步间隔已调整为5分钟
- UserInfoActivity入口分流逻辑已完善：修复了5分钟倒计时结束和用户数据异常时的处理逻辑，统一使用handleExitByEntryType()方法根据入口类型处理，COUNTDOWN_ENDED_ENTRY入口切换到后台，NORMAL_ENTRY入口退出页面，避免意外启动数据清理流程；同时修复了Activity复用时的竞态条件，在onPause()中停止页面倒计时，符合Android生命周期最佳实践
