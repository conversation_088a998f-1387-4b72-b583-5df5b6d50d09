[versions]
activityKtx = "1.10.1"
agp = "8.11.0"
binding = "1.2.0"
cardview = "1.0.0"
coil = "2.7.0"
core = "3.5.3"
dialogx = "0.0.49"

kotlin = "2.2.0"
coreKtx = "1.16.0"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.6.1"
appcompat = "1.7.1"
kotlinxCoroutinesAndroid = "1.10.2"
kotlinxCoroutinesCore = "1.10.2"
ktor = "3.2.2"
lifecycleLivedataKtx = "2.9.2"
material = "1.12.0"
mmkv = "2.2.2"
toaster = "13.2"
uuidCreator = "6.1.1"
xlog = "1.11.1"
xupdate = "2.1.5"

[libraries]
androidx-activity-ktx = { module = "androidx.activity:activity-ktx", version.ref = "activityKtx" }
androidx-cardview = { module = "androidx.cardview:cardview", version.ref = "cardview" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }

androidx-lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycleLivedataKtx" }
androidx-lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycleLivedataKtx" }
binding = { module = "com.hi-dhl:binding", version.ref = "binding" }
coil = { module = "io.coil-kt:coil", version.ref = "coil" }
core = { module = "com.google.zxing:core", version.ref = "core" }
dialogx = { module = "com.kongzue.dialogx:DialogX", version.ref = "dialogx" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "kotlinxCoroutinesAndroid" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktor" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktor" }
ktor-client-websockets = { module = "io.ktor:ktor-client-websockets", version.ref = "ktor" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
mmkv = { module = "com.tencent:mmkv", version.ref = "mmkv" }
toaster = { module = "com.github.getActivity:Toaster", version.ref = "toaster" }
uuid-creator = { module = "com.github.f4b6a3:uuid-creator", version.ref = "uuidCreator" }
xlog = { module = "com.elvishew:xlog", version.ref = "xlog" }
xupdate = { module = "com.github.xuexiangjys:XUpdate", version.ref = "xupdate" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }

