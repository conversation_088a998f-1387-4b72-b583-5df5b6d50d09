package com.hwb.timecontroller.service;

/**
 * 应用启动权限验证服务接口
 * 为第三方应用（如Unity）提供同步的应用启动权限验证服务
 *
 * Author: huangwubin
 * Contacts: <EMAIL>
 *
 * changeLogs:
 * 2025/7/7: First created this interface for AIDL Service implementation.
 */
interface ICheckService {

    /**
     * 检查应用启动权限
     * @param need_show_lock 是否需要显示锁定界面进行验证
     * @return true-允许启动, false-不允许启动
     */
    boolean check(boolean need_show_lock);

    /**
     * 跳转到指定页面
     * @param pageType 页面类型。1. 用户信息页面（未登录时会跳到登录页面） 2. 设置页面（自动校验密码）
     * @return true-跳转成功, false-跳转失败
     */
    boolean jumpToPage(int pageType);
}
