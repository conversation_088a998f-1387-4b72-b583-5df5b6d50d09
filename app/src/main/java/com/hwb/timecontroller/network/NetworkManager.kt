package com.hwb.timecontroller.network

import com.hwb.timecontroller.network.NetworkConfig.CONNECT_TIMEOUT_MS
import com.hwb.timecontroller.network.NetworkConfig.REQUEST_TIMEOUT_MS
import com.hwb.timecontroller.network.NetworkConfig.SOCKET_TIMEOUT_MS
import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.plugins.websocket.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json
import com.elvishew.xlog.XLog
import kotlin.time.Duration.Companion.milliseconds

/**
 * 统一网络管理器
 * 提供HTTP和WebSocket客户端
 */
object NetworkManager {
    
    /**
     * HTTP客户端 - 使用OkHttp引擎（统一引擎，支持所有功能）
     */
    val httpClient: HttpClient by lazy {
        HttpClient(OkHttp) {
            // 内容协商
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                    encodeDefaults = false
                })
            }

            // 超时配置
            install(HttpTimeout) {
                requestTimeoutMillis = NetworkConfig.REQUEST_TIMEOUT_MS
                connectTimeoutMillis = NetworkConfig.CONNECT_TIMEOUT_MS
                socketTimeoutMillis = NetworkConfig.SOCKET_TIMEOUT_MS
            }

            // 注意：不使用defaultRequest，直接在请求时拼接完整URL

            // 日志配置
            install(Logging) {
                level = LogLevel.INFO
                logger = object : Logger {
                    override fun log(message: String) {
                        XLog.d("HTTP: $message")
                    }
                }
            }
        }
    }
    
    /**
     * WebSocket客户端 - 使用OkHttp引擎以获得更好的WebSocket支持
     */
    val webSocketClient: HttpClient by lazy {
        HttpClient(OkHttp) {
            // WebSocket支持
            install(WebSockets) {
                pingInterval = NetworkConfig.WS_PING_INTERVAL_MS.milliseconds
            }

            // 超时配置
            install(HttpTimeout) {
                requestTimeoutMillis = NetworkConfig.REQUEST_TIMEOUT_MS
                connectTimeoutMillis = NetworkConfig.CONNECT_TIMEOUT_MS
                socketTimeoutMillis = NetworkConfig.SOCKET_TIMEOUT_MS
            }

            // 日志配置
            install(Logging) {
                level = LogLevel.INFO
                logger = object : Logger {
                    override fun log(message: String) {
                        XLog.d("WebSocket: $message")
                    }
                }
            }
        }
    }
    
    /**
     * 创建支持HTTP明文通信的客户端
     * 专门用于远程白名单等HTTP接口
     */
    fun createHttpOnlyClient(): HttpClient {
        return HttpClient(OkHttp) {
            // 内容协商
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                    encodeDefaults = false
                })
            }

            // 超时配置
            install(HttpTimeout) {
                requestTimeoutMillis = REQUEST_TIMEOUT_MS
                connectTimeoutMillis = CONNECT_TIMEOUT_MS
                socketTimeoutMillis = SOCKET_TIMEOUT_MS
            }

            // 日志配置
            install(Logging) {
                level = LogLevel.INFO
                logger = object : Logger {
                    override fun log(message: String) {
                        XLog.d("HTTP-Only: $message")
                    }
                }
            }
        }
    }

    /**
     * 关闭所有客户端
     */
    fun closeAll() {
        try {
            httpClient.close()
            webSocketClient.close()
            XLog.d("网络客户端已关闭")
        } catch (e: Exception) {
            XLog.e("关闭网络客户端失败", e)
        }
    }
}
