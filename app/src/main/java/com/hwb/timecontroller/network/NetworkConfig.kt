package com.hwb.timecontroller.network

/**
 * 网络配置常量
 */
object NetworkConfig {

    // 服务器配置
    const val BASE_URL = "https://game.3ddu.cn"
    const val BASE_PATH = "/estate"
    const val WEBSOCKET_URL = "wss://api.example.com/ws"

    // API端点 - 相对路径，需要手动拼接完整URL
    // 使用方式: BASE_URL + BASE_PATH + "/" + ENDPOINT_CLIENT_LOGIN
    // 最终URL: https://game.3ddu.cn/estate/v1/clientLogin
    const val ENDPOINT_CLIENT_LOGIN = "v1/clientLogin"

    // 查询用户余额接口
    // 最终URL: https://game.3ddu.cn/estate/v1/getTimeFromBalance
    const val ENDPOINT_QUERY_BALANCE = "v1/getTimeFromBalance"

    // 扣款接口
    // 最终URL: https://game.3ddu.cn/estate/v1/deductMoney
    const val ENDPOINT_DEDUCT_MONEY = "v1/deductMoney"

    // 退出登录接口
    // 最终URL: https://game.3ddu.cn/estate/v1/logout
    const val ENDPOINT_LOGOUT = "v1/logout"

    // 远程白名单接口
    // 最终URL: http://vr.infinitus-test.cn/VR/GetApp.php
    const val REMOTE_WHITELIST_BASE_URL = "http://vr.infinitus-test.cn"
    const val ENDPOINT_REMOTE_WHITELIST = "/VR/GetApp.php"

    // 超时配置
    const val CONNECT_TIMEOUT_MS = 10_000L
    const val REQUEST_TIMEOUT_MS = 30_000L
    const val SOCKET_TIMEOUT_MS = 60_000L
    
    // WebSocket配置
    const val WS_PING_INTERVAL_MS = 30_000L
    const val WS_RECONNECT_DELAY_MS = 5_000L
    const val WS_MAX_RECONNECT_ATTEMPTS = 5

    // 远程白名单更新配置
    const val REMOTE_WHITELIST_UPDATE_INTERVAL_MS = 10 * 60 * 1000L // 10分钟
}
