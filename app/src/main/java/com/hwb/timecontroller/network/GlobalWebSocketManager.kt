package com.hwb.timecontroller.network

import io.ktor.client.plugins.websocket.*
import io.ktor.http.*
import io.ktor.websocket.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString
import com.elvishew.xlog.XLog

/**
 * 全局WebSocket管理器
 * 提供统一的WebSocket连接和消息分发机制
 */
object GlobalWebSocketManager {

    private val webSocketClient = NetworkManager.webSocketClient
    private var webSocketSession: DefaultClientWebSocketSession? = null
    private var connectionJob: Job? = null
    private var reconnectJob: Job? = null
    private var isConnected = false
    private var shouldReconnect = true
    private var reconnectAttempts = 0

    // 消息分发流
    private val _messageFlow = MutableSharedFlow<WebSocketMessage>()
    val messageFlow: SharedFlow<WebSocketMessage> = _messageFlow.asSharedFlow()

    // 连接状态流
    private val _connectionStateFlow = MutableSharedFlow<WebSocketConnectionState>()
    val connectionStateFlow: SharedFlow<WebSocketConnectionState> = _connectionStateFlow.asSharedFlow()

    /**
     * 连接WebSocket
     */
    suspend fun connect() {
        if (isConnected) {
            XLog.d("WebSocket已连接，忽略重复连接请求")
            return
        }

        shouldReconnect = true
        reconnectAttempts = 0
        connectInternal()
    }

    /**
     * 内部连接方法
     */
    private suspend fun connectInternal() {
        try {
            XLog.d("开始连接全局WebSocket")
            _connectionStateFlow.emit(WebSocketConnectionState.CONNECTING)
            
            connectionJob = CoroutineScope(Dispatchers.IO).launch {
                try {
                    webSocketClient.webSocket(
                        method = HttpMethod.Get,
                        host = getHostFromUrl(NetworkConfig.WEBSOCKET_URL),
                        port = getPortFromUrl(NetworkConfig.WEBSOCKET_URL),
                        path = "/ws"
                    ) {
                    webSocketSession = this
                    isConnected = true
                    reconnectAttempts = 0
                    
                    XLog.d("全局WebSocket连接成功")
                    _connectionStateFlow.emit(WebSocketConnectionState.CONNECTED)
                    
                    // 发送心跳
                    startHeartbeat()
                    
                    // 监听消息
                    try {
                        for (frame in incoming) {
                            when (frame) {
                                is Frame.Text -> {
                                    val messageText = frame.readText()
                                    handleIncomingMessage(messageText)
                                }
                                is Frame.Close -> {
                                    XLog.d("WebSocket连接关闭")
                                    handleConnectionClosed()
                                    break
                                }
                                else -> {
                                    // 忽略其他类型的帧
                                }
                            }
                        }
                    } catch (e: Exception) {
                        XLog.e("WebSocket消息处理异常", e)
                        handleConnectionError(e)
                    }
                }
                } catch (e: Exception) {
                    XLog.e("WebSocket协程内部连接失败", e)
                    handleConnectionError(e)
                }
            }
            
        } catch (e: Exception) {
            XLog.e("WebSocket连接失败", e)
            handleConnectionError(e)
        }
    }

    /**
     * 处理接收到的消息
     */
    private suspend fun handleIncomingMessage(messageText: String) {
        try {
            XLog.d("收到WebSocket消息: $messageText")
            
            val message = Json.decodeFromString<WebSocketMessage>(messageText)
            _messageFlow.emit(message)
            
        } catch (e: Exception) {
            XLog.e("解析WebSocket消息失败: $messageText", e)
        }
    }

    /**
     * 处理连接关闭
     */
    private suspend fun handleConnectionClosed() {
        isConnected = false
        webSocketSession = null
        _connectionStateFlow.emit(WebSocketConnectionState.DISCONNECTED)
        
        if (shouldReconnect && reconnectAttempts < NetworkConfig.WS_MAX_RECONNECT_ATTEMPTS) {
            scheduleReconnect()
        }
    }

    /**
     * 处理连接错误 - 增强异常处理
     */
    private suspend fun handleConnectionError(error: Throwable) {
        try {
            isConnected = false
            webSocketSession = null
            _connectionStateFlow.emit(WebSocketConnectionState.ERROR)

            // 根据错误类型记录不同级别的日志
            when (error) {
                is java.net.UnknownHostException -> {
                    XLog.w("WebSocket域名解析失败，可能是网络配置问题", error)
                }
                is java.net.ConnectException -> {
                    XLog.w("WebSocket连接被拒绝，服务器可能不可用", error)
                }
                is java.net.SocketTimeoutException -> {
                    XLog.w("WebSocket连接超时", error)
                }
                else -> {
                    XLog.e("WebSocket连接发生未知错误: ${error.javaClass.simpleName}", error)
                }
            }

            if (shouldReconnect && reconnectAttempts < NetworkConfig.WS_MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect()
            } else {
                XLog.w("WebSocket重连次数已达上限(${NetworkConfig.WS_MAX_RECONNECT_ATTEMPTS})，停止重连")
            }
        } catch (e: Exception) {
            // 确保错误处理本身不会抛出异常
            XLog.e("处理WebSocket连接错误时发生异常", e)
        }
    }

    /**
     * 安排重连
     */
    private suspend fun scheduleReconnect() {
        reconnectAttempts++
        
        XLog.d("安排WebSocket重连，第${reconnectAttempts}次尝试")
        
        reconnectJob = CoroutineScope(Dispatchers.IO).launch {
            delay(NetworkConfig.WS_RECONNECT_DELAY_MS)
            
            if (shouldReconnect) {
                connectInternal()
            }
        }
    }

    /**
     * 开始心跳
     */
    private fun startHeartbeat() {
        CoroutineScope(Dispatchers.IO).launch {
            while (isConnected && webSocketSession != null) {
                try {
                    val heartbeatMessage = WebSocketMessage(
                        type = "heartbeat",
                        data = "ping"
                    )
                    
                    val messageJson = Json.encodeToString(heartbeatMessage)
                    webSocketSession?.send(Frame.Text(messageJson))
                    
                    delay(NetworkConfig.WS_PING_INTERVAL_MS)
                    
                } catch (e: Exception) {
                    XLog.e("发送心跳失败", e)
                    break
                }
            }
        }
    }

    /**
     * 发送消息
     */
    suspend fun sendMessage(message: WebSocketMessage) {
        try {
            if (isConnected && webSocketSession != null) {
                val messageJson = Json.encodeToString(message)
                webSocketSession?.send(Frame.Text(messageJson))
                XLog.d("发送WebSocket消息: $messageJson")
            } else {
                XLog.w("WebSocket未连接，无法发送消息")
            }
        } catch (e: Exception) {
            XLog.e("发送WebSocket消息失败", e)
        }
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        shouldReconnect = false
        isConnected = false
        
        try {
            connectionJob?.cancel()
            reconnectJob?.cancel()
            
            CoroutineScope(Dispatchers.IO).launch {
                webSocketSession?.close(CloseReason(CloseReason.Codes.NORMAL, "客户端主动断开"))
                webSocketSession = null
                _connectionStateFlow.emit(WebSocketConnectionState.DISCONNECTED)
            }
            
            XLog.d("全局WebSocket连接已断开")
            
        } catch (e: Exception) {
            XLog.e("断开WebSocket连接失败", e)
        }
    }

    /**
     * 从URL中提取主机名
     */
    private fun getHostFromUrl(url: String): String {
        return try {
            url.substringAfter("://").substringBefore("/").substringBefore(":")
        } catch (e: Exception) {
            "localhost"
        }
    }

    /**
     * 从URL中提取端口号
     */
    private fun getPortFromUrl(url: String): Int {
        return try {
            val hostPort = url.substringAfter("://").substringBefore("/")
            if (hostPort.contains(":")) {
                hostPort.substringAfter(":").toInt()
            } else {
                if (url.startsWith("wss://")) 443 else 80
            }
        } catch (e: Exception) {
            80
        }
    }

    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean = isConnected
}

/**
 * WebSocket连接状态
 */
enum class WebSocketConnectionState {
    DISCONNECTED,   // 未连接
    CONNECTING,     // 连接中
    CONNECTED,      // 已连接
    ERROR           // 连接错误
}
