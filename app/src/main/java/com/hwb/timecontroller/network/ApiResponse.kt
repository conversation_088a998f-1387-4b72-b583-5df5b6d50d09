package com.hwb.timecontroller.network

import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonDecoder
import kotlinx.serialization.json.JsonNull
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.doubleOrNull
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.longOrNull
import kotlinx.serialization.serializer
import java.io.Serializable as JavaSerializable

/**
 * API响应基类
 */
@Serializable
data class ApiResponse<T>(
    val success: Boolean,
    val message: String,
    val data: T? = null,
    val code: Int = 0
)

/**
 * 二维码请求数据
 */
@Serializable
data class QRCodeRequest(
    val deviceId: String,
    val appPackage: String,
    val appName: String
)

/**
 * 二维码响应数据
 */
@Serializable
data class QRCodeResponse(
    val orderId: String,
    val qrCodeBase64: String,  // 改为base64格式
    val expireTime: Long,
    val amount: Double = 0.0,
    val appName: String = ""
)

/**
 * 登录状态响应数据
 */
@Serializable
data class LoginStatusResponse(
    val orderId: String,
    val status: String, // pending, success, failed, expired
    val message: String = "",
    val loginTime: Long? = null,
    val addedMinutes: Int = 0 // 登录成功后增加的倒计时分钟数
)

/**
 * 付款状态响应数据（保留兼容性）
 */
@Serializable
data class PaymentStatusResponse(
    val orderId: String,
    val status: String, // pending, success, failed, expired
    val message: String = "",
    val paidTime: Long? = null,
    val addedMinutes: Int = 0 // 付费成功后增加的倒计时分钟数
)

/**
 * WebSocket消息类型
 */
@Serializable
data class WebSocketMessage(
    val type: String, // payment_status, heartbeat, error
    val data: String = "",
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 登录状态枚举
 */
enum class LoginStatus(val value: String) {
    PENDING("pending"),
    SUCCESS("success"),
    FAILED("failed"),
    EXPIRED("expired");

    companion object {
        fun fromString(value: String): LoginStatus {
            return values().find { it.value == value } ?: PENDING
        }
    }
}

/**
 * 付款状态枚举（保留兼容性）
 */
enum class PaymentStatus(val value: String) {
    PENDING("pending"),
    SUCCESS("success"),
    FAILED("failed"),
    EXPIRED("expired");

    companion object {
        fun fromString(value: String): PaymentStatus {
            return values().find { it.value == value } ?: PENDING
        }
    }
}

/**
 * 网络请求结果封装
 */
sealed class NetworkResult<T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error<T>(val message: String, val code: Int = -1) : NetworkResult<T>()
    data class Loading<T>(val message: String = "加载中...") : NetworkResult<T>()
}

/**
 * 客户端登录响应数据（基于实际API响应）
 */
@Serializable
data class ClientLoginResponse(
    val code: Int,
    val msg: String,
    @Serializable(with = FlexibleClientLoginDataSerializer::class)
    val rspdata: ClientLoginData? = null,
    val rsptime: Long,
    val jwt: String? = null
)

/**
 * 查询用户余额响应数据
 */
@Serializable
data class QueryBalanceResponse(
    val code: Int,
    val msg: String,
    @Serializable(with = FlexibleClientLoginDataSerializer::class)
    val rspdata: ClientLoginData? = null,
    val rsptime: Long
)

/**
 * 退出登录响应数据
 */
@Serializable
data class LogoutResponse(
    val code: Int,
    val msg: String,
    @Serializable(with = FlexibleLogoutDataSerializer::class)
    val rspdata: LogoutData? = null,
    val rsptime: Long,
    val jwt: String? = null
)

/**
 * 余额时间查询响应数据
 * 对应新接口: https://game.3ddu.cn/estate/v1/getTimeFromBalance?userId=xxx
 */
@Serializable
data class BalanceTimeResponse(
    val code: Int,
    val msg: String,
    @Serializable(with = FlexibleBalanceTimeDataSerializer::class)
    val rspdata: BalanceTimeData? = null,
    val jwt: String? = null,
    val rsptime: Long
)

/**
 * 余额时间数据
 */
@Serializable
data class BalanceTimeData(
    val timeInMinutes: Int,
    val balance: Double
) : JavaSerializable

/**
 * 扣款请求数据
 */
@Serializable
data class DeductMoneyRequest(
    val userId: String,
    val amount: Double
) : JavaSerializable

/**
 * 扣款响应数据
 */
@Serializable
data class DeductMoneyResponse(
    val code: Int,
    val msg: String,
    @Serializable(with = FlexibleDeductMoneyDataSerializer::class)
    val rspdata: DeductMoneyData? = null,
    val jwt: String? = null,
    val rsptime: Long
)

/**
 * 扣款结果数据
 */
@Serializable
data class DeductMoneyData(
    val consumedTotal: Double? = null,    // 总消费金额
    val newBalance: Double? = null,       // 新余额
    val userId: String? = null,           // 用户ID
    val transactionId: String? = null     // 交易ID（保留兼容性）
) : JavaSerializable {

    // 兼容性属性，映射到新字段
    val remainingBalance: Double? get() = newBalance
    val deductedAmount: Double? get() = consumedTotal
    val success: Boolean get() = consumedTotal != null && newBalance != null
}

/**
 * 客户端登录响应数据部分
 */
@Serializable
data class ClientLoginData(
    val id: String? = null,
    val openId: String? = null,
    val money: Double? = null, // 改为Double类型以兼容服务器返回的浮点数
    val userName: String? = null,
    val headImg: String? = null,
    val createTime: String? = null,
    val temp_time: Int? = null // 剩余时长（秒），临时实现为 money * 10
) : JavaSerializable

/**
 * 退出登录响应数据部分
 */
@Serializable
data class LogoutData(
    val amount: Double? = null,
    val orderId: String? = null,
    val userId: String? = null
) : JavaSerializable

/**
 * 通用的灵活序列化器
 * 处理API响应中可能为对象、字符串、数字、数组等不同类型的情况
 */
class FlexibleSerializer<T : Any>(
    private val targetSerializer: KSerializer<T>,
    private val primitiveHandler: (JsonPrimitive) -> T? = { null }
) : KSerializer<T?> {

    override val descriptor: SerialDescriptor = targetSerializer.descriptor

    override fun serialize(encoder: Encoder, value: T?) {
        if (value == null) {
            encoder.encodeNull()
        } else {
            encoder.encodeSerializableValue(targetSerializer, value)
        }
    }

    override fun deserialize(decoder: Decoder): T? {
        return try {
            val jsonDecoder = decoder as JsonDecoder
            val element = jsonDecoder.decodeJsonElement()

            when {
                // 如果是null，返回null
                element is JsonNull -> null

                // 如果是对象，正常反序列化
                element is JsonObject -> {
                    jsonDecoder.json.decodeFromJsonElement(targetSerializer, element)
                }

                // 如果是数组，尝试反序列化（如果目标类型支持）
                element is JsonArray -> {
                    try {
                        jsonDecoder.json.decodeFromJsonElement(targetSerializer, element)
                    } catch (e: Exception) {
                        null
                    }
                }

                // 如果是基础类型，使用自定义处理器
                element is JsonPrimitive -> {
                    primitiveHandler(element)
                }

                // 其他情况返回null
                else -> null
            }
        } catch (e: Exception) {
            // 反序列化失败时返回null，避免崩溃
            null
        }
    }
}



/**
 * 灵活的ClientLoginData序列化器
 * 处理服务器返回空字符串等异常情况
 */
object FlexibleClientLoginDataSerializer : KSerializer<ClientLoginData?> by FlexibleSerializer(
    targetSerializer = ClientLoginData.serializer(),
    primitiveHandler = emptyStringToNullHandler
)

/**
 * 灵活的LogoutData序列化器
 * 处理服务器返回空字符串等异常情况
 */
object FlexibleLogoutDataSerializer : KSerializer<LogoutData?> by FlexibleSerializer(
    targetSerializer = LogoutData.serializer(),
    primitiveHandler = emptyStringToNullHandler
)

/**
 * 通用的空字符串处理器
 * 处理服务器返回空字符串或其他异常情况，统一返回null
 */
val emptyStringToNullHandler: (JsonPrimitive) -> Nothing? = { element ->
    when {
        // 处理空字符串情况
        element.isString && element.content.isEmpty() -> null
        // 处理其他字符串情况
        element.isString -> null
        // 处理数字类型（如果服务器直接返回数字）
        !element.isString -> null
        else -> null
    }
}

/**
 * 创建处理空字符串的灵活序列化器的工厂方法
 * 用于统一处理服务器返回空字符串的情况
 */
inline fun <reified T : Any> createEmptyStringTolerantSerializer(): KSerializer<T?> {
    return FlexibleSerializer(
        targetSerializer = serializer<T>(),
        primitiveHandler = emptyStringToNullHandler
    )
}

/**
 * 灵活的BalanceTimeData序列化器
 * 处理服务器返回空字符串等异常情况
 */
object FlexibleBalanceTimeDataSerializer :
    KSerializer<BalanceTimeData?> by createEmptyStringTolerantSerializer<BalanceTimeData>()

/**
 * 灵活的DeductMoneyData序列化器
 * 处理服务器返回空字符串等异常情况
 */
object FlexibleDeductMoneyDataSerializer :
    KSerializer<DeductMoneyData?> by createEmptyStringTolerantSerializer<DeductMoneyData>()

/**
 * 远程应用信息数据类
 * 用于接收远程白名单API响应
 */
@Serializable
data class RemoteAppInfo(
    val id: String,
    val appName: String,
    val packageName: String
)

/**
 * 远程白名单响应类型别名
 * API直接返回RemoteAppInfo数组
 */
typealias RemoteWhitelistResponse = List<RemoteAppInfo>

/**
 * 应用更新检查响应数据
 * 对应接口: https://game.3ddu.cn/estate/v1/checkUpdate
 */
@Serializable
data class UpdateCheckResponse(
    val code: Int,
    val msg: String,
    val rspdata: UpdateInfo? = null,
    val jwt: String? = null,
    val rsptime: Long
)

/**
 * 更新信息数据
 */
@Serializable
data class UpdateInfo(
    val latestVersion: String,
    val downloadUrl: String,
    val forceUpdate: Boolean,
    val updateContent: String
) : JavaSerializable

/**
 * 使用示例：为其他数据类型创建灵活序列化器
 *
 * // 示例1：使用工厂方法创建处理空字符串的序列化器（推荐）
 * object FlexibleUserInfoSerializer : KSerializer<UserInfo?> by createEmptyStringTolerantSerializer<UserInfo>()
 *
 * // 示例2：自定义处理逻辑的序列化器
 * object FlexibleOrderSerializer : KSerializer<Order?> by FlexibleSerializer(
 *     targetSerializer = Order.serializer(),
 *     primitiveHandler = { element ->
 *         element.longOrNull?.let { orderId ->
 *             Order(id = orderId)
 *         }
 *     }
 * )
 *
 * // 注意：由于服务器接口设计不规范，在错误情况下可能返回空字符串而不是null或对象
 * // 对于所有包含rspdata字段的响应数据类，建议使用createEmptyStringTolerantSerializer()
 * // 或FlexibleSerializer来避免JSON反序列化异常
 */
