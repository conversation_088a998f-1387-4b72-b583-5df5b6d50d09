package com.hwb.timecontroller.network

import com.xuexiang.xupdate.proxy.IUpdateHttpService
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.contentType
import io.ktor.http.formUrlEncode
import kotlinx.coroutines.runBlocking
import com.elvishew.xlog.XLog

/**
 * XUpdate的HTTP服务适配器
 * 使用Ktor Client实现XUpdate的网络请求接口
 */
class OkHttpUpdateHttpService(private val httpClient: HttpClient) : IUpdateHttpService {

    override fun asyncGet(
        url: String,
        params: Map<String?, Any?>,
        callBack: IUpdateHttpService.Callback
    ) {
        try {
            runBlocking {
                val response = httpClient.get(url) {
                    // 添加查询参数
                    params.forEach { (key, value) ->
                        key?.let { parameter(it, value.toString()) }
                    }
                }

                if (response.status == HttpStatusCode.OK) {
                    val responseBody = response.body<String>()
                    callBack.onSuccess(responseBody)
                } else {
                    callBack.onError(Exception("HTTP ${response.status.value}: ${response.status.description}"))
                }
            }
        } catch (e: Exception) {
            XLog.e("GET请求失败: $url", e)
            callBack.onError(e)
        }

    }

    override fun asyncPost(
        url: String,
        params: Map<String?, Any?>,
        callBack: IUpdateHttpService.Callback
    ) {
        try {
            runBlocking {
                val response = httpClient.post(url) {
                    contentType(ContentType.Application.FormUrlEncoded)

                    // 添加表单参数
                    params.let { paramMap ->
                        val formParameters = Parameters.build {
                            paramMap.forEach { (key, value) ->
                                key?.let {
                                    append(it, value.toString())
                                }
                            }
                        }
                        setBody(formParameters.formUrlEncode())
                    }
                }

                if (response.status == HttpStatusCode.OK) {
                    val responseBody = response.body<String>()
                    callBack.onSuccess(responseBody)
                } else {
                    callBack.onError(Exception("HTTP ${response.status.value}: ${response.status.description}"))
                }
            }
        } catch (e: Exception) {
            XLog.e("POST请求失败: $url", e)
            callBack.onError(e)
        }
    }

    override fun download(
        url: String,
        path: String,
        fileName: String,
        callback: IUpdateHttpService.DownloadCallback
    ) {
        try {
            runBlocking {
                val response = httpClient.get(url)

                if (response.status == HttpStatusCode.OK) {
                    val bytes = response.body<ByteArray>()

                    val saveFilePath = if (path.endsWith("/")) {
                        "$path$fileName"
                    } else {
                        "$path/$fileName"
                    }

                    // 写入文件
                    val file = java.io.File(saveFilePath)
                    file.parentFile?.mkdirs()
                    file.writeBytes(bytes)

                    // 通知下载完成
                    callback.onSuccess(file)
                } else {
                    callback.onError(Exception("下载失败: HTTP ${response.status.value}"))
                }
            }
        } catch (e: Exception) {
            XLog.e("下载文件失败: $url", e)
            callback.onError(e)
        }

    }

    override fun cancelDownload(url: String) {
        // Ktor Client的取消机制比较复杂，这里暂时不实现
        // 在实际使用中，可以通过Job来管理协程的取消
        XLog.d("取消下载请求: $url")
    }
}
