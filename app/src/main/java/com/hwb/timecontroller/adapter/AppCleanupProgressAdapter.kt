package com.hwb.timecontroller.adapter

import android.content.pm.PackageManager
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.hwb.timecontroller.R
import com.hwb.timecontroller.model.CleanupProgressItem
import com.elvishew.xlog.XLog

/**
 * 应用数据清理进度适配器
 * <p>
 * Author:huangwubin
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/8: First created this class.
 */
class AppCleanupProgressAdapter : ListAdapter<CleanupProgressItem, AppCleanupProgressAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_cleanup_progress, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val ivAppIcon: ImageView = itemView.findViewById(R.id.iv_app_icon)
        private val tvAppName: TextView = itemView.findViewById(R.id.tv_app_name)
        private val tvPackageName: TextView = itemView.findViewById(R.id.tv_package_name)
        private val progressCleanup: ProgressBar = itemView.findViewById(R.id.progress_cleanup)
        private val tvStatus: TextView = itemView.findViewById(R.id.tv_status)

        fun bind(item: CleanupProgressItem) {
            try {
                // 设置应用名称和包名
                tvAppName.text = item.appName
                tvPackageName.text = item.packageName

                // 设置应用图标
                try {
                    val packageManager = itemView.context.packageManager
                    val appInfo = packageManager.getApplicationInfo(item.packageName, 0)
                    val icon = packageManager.getApplicationIcon(appInfo)
                    ivAppIcon.setImageDrawable(icon)
                } catch (e: PackageManager.NameNotFoundException) {
                    // 应用未找到，使用默认图标
                    ivAppIcon.setImageResource(R.drawable.ic_launcher_foreground)
                    XLog.w("应用图标未找到: ${item.packageName}")
                }

                // 设置进度
                progressCleanup.progress = item.progress

                // 设置状态文本和颜色
                tvStatus.text = item.getStatusText()
                val statusColor = ContextCompat.getColor(itemView.context, item.getStatusColorRes())
                tvStatus.setTextColor(statusColor)

                // 根据状态调整进度条可见性
                when (item.status) {
                    CleanupProgressItem.CleanupStatus.WAITING -> {
                        progressCleanup.visibility = View.GONE
                    }
                    CleanupProgressItem.CleanupStatus.CLEANING -> {
                        progressCleanup.visibility = View.VISIBLE
                        progressCleanup.isIndeterminate = false
                    }
                    CleanupProgressItem.CleanupStatus.SUCCESS,
                    CleanupProgressItem.CleanupStatus.FAILED -> {
                        progressCleanup.visibility = View.VISIBLE
                        progressCleanup.isIndeterminate = false
                        progressCleanup.progress = 100
                    }
                }

            } catch (e: Exception) {
                XLog.e("绑定清理进度项失败: ${item.packageName}", e)
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<CleanupProgressItem>() {
        override fun areItemsTheSame(oldItem: CleanupProgressItem, newItem: CleanupProgressItem): Boolean {
            return oldItem.packageName == newItem.packageName
        }

        override fun areContentsTheSame(oldItem: CleanupProgressItem, newItem: CleanupProgressItem): Boolean {
            return oldItem == newItem
        }
    }
}
