package com.hwb.timecontroller.business

import android.content.ContentValues
import android.content.Context
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import com.elvishew.xlog.XLog
import com.github.f4b6a3.uuid.UuidCreator
import com.hwb.timecontroller.constant.UserStorageKeys
import com.hwb.timecontroller.model.UserInfo
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import io.ktor.client.call.body
import io.ktor.client.request.forms.submitForm
import java.io.File
import java.io.FileOutputStream
import java.security.MessageDigest
import java.util.UUID

/**
 * 用户管理器
 *
 * 功能：
 * - 设备UUID生成与管理（UUIDv7算法）
 * - 双重存储策略（MMKV主存储 + MediaStore备份存储）
 * - 用户登录状态管理
 * - 用户信息存储与获取
 * - 统一的用户相关API接口
 *
 * Author: huangwubin
 * Date: 2025/7/4
 */
object UserManager {

    // MMKV实例
    private val mmkv: MMKV by lazy { MMKV.defaultMMKV() }

    // 协程作用域
    private val managerScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

    // 应用上下文
    private var appContext: Context? = null

    // 当前用户信息缓存
    @Volatile
    private var currentUserInfo: UserInfo? = null

    // 完整的客户端登录数据缓存（内存中，不持久化）
    @Volatile
    private var currentClientLoginData: com.hwb.timecontroller.network.ClientLoginData? = null

    // 设备ID缓存
    @Volatile
    private var cachedDeviceId: String? = null

    // 随机码缓存（6位数字+字母，区分大小写）
    @Volatile
    private var cachedRandomCode: String? = null

    // 用户登录状态变化监听器
    private val loginStatusListeners = mutableSetOf<UserLoginStatusListener>()

    /**
     * 初始化UserManager
     * @param context 应用上下文
     */
    fun initialize(context: Context) {
        appContext = context.applicationContext
        XLog.d("UserManager初始化完成")

        // 异步初始化设备ID和用户信息
        managerScope.launch {
            try {
                // 确保设备ID存在
                getDeviceIdAsync()

                // 初始化余额轮询管理器
                BalancePollingManager.initialize()

                XLog.d("UserManager异步初始化完成")
            } catch (e: Exception) {
                XLog.e("UserManager异步初始化失败", e)
            }
        }
    }

    /**
     * 获取设备ID（同步方法）
     * @return 设备UUID字符串
     */
    fun getDeviceId(): String {
        // 优先返回缓存的设备ID
        cachedDeviceId?.let { return it }

        return try {
            // 1. 尝试从MMKV读取
            val mmkvDeviceId = mmkv.getString(UserStorageKeys.MMKV.USER_DEVICE_UUID, null)
            if (!mmkvDeviceId.isNullOrEmpty()) {
                cachedDeviceId = mmkvDeviceId
                XLog.d("从MMKV获取设备ID: $mmkvDeviceId")
                return mmkvDeviceId
            }

            // 2. 尝试从MediaStore备份文件读取
            val backupDeviceId = readDeviceIdFromMediaStore()
            if (!backupDeviceId.isNullOrEmpty()) {
                // 恢复到MMKV
                mmkv.putString(UserStorageKeys.MMKV.USER_DEVICE_UUID, backupDeviceId)
                cachedDeviceId = backupDeviceId
                XLog.d("从MediaStore备份恢复设备ID: $backupDeviceId")
                return backupDeviceId
            }

            // 3. 生成新的设备ID
            val newDeviceId = generateAndSaveDeviceId()
            cachedDeviceId = newDeviceId
            XLog.d("生成新的设备ID: $newDeviceId")
            newDeviceId

        } catch (e: Exception) {
            XLog.e("获取设备ID失败，使用随机UUID", e)
            val fallbackId = UUID.randomUUID().toString()
            cachedDeviceId = fallbackId
            fallbackId
        }
    }

    /**
     * 获取设备ID（异步方法）
     * @return 设备UUID字符串
     */
    suspend fun getDeviceIdAsync(): String = withContext(Dispatchers.IO) {
        getDeviceId()
    }

    /**
     * 获取secretKey（设备ID + "&" + 6位随机码）
     * @return secretKey字符串
     */
    fun getSecretKey(): String {
        return try {
            val deviceId = getDeviceId()
            val randomCode = getRandomCode()
            "${deviceId}_$randomCode"
        } catch (e: Exception) {
            XLog.e("获取secretKey失败", e)
            "${getDeviceId()}&${generateRandomCode()}"
        }
    }

    /**
     * 获取6位随机码（数字+大小写字母）
     * @return 6位随机码
     */
    private fun getRandomCode(): String {
        // 如果缓存中有随机码，直接返回
        cachedRandomCode?.let { return it }

        // 生成新的随机码
        val newRandomCode = generateRandomCode()
        cachedRandomCode = newRandomCode
        XLog.d("生成新的随机码: $newRandomCode")
        return newRandomCode
    }

    /**
     * 生成6位随机码（数字+大小写字母）
     * @return 6位随机码
     */
    private fun generateRandomCode(): String {
        val chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
        return (1..6)
            .map { chars.random() }
            .joinToString("")
    }

    /**
     * 重置随机码（退出登录时调用）
     */
    private fun resetRandomCode() {
        cachedRandomCode = null
        XLog.d("随机码已重置")
    }

    /**
     * 刷新设备ID（重新生成）
     * 注意：只有在特殊情况下才应该调用此方法
     * @return 新的设备UUID字符串
     */
    fun refreshDeviceId(): String {
        return try {
            XLog.w("强制刷新设备ID")
            cachedDeviceId = null
            val newDeviceId = generateAndSaveDeviceId()
            cachedDeviceId = newDeviceId

            // 更新用户信息中的设备ID
            currentUserInfo?.let { userInfo ->
                val updatedUserInfo = userInfo.copy(deviceId = newDeviceId)
                saveUserInfo(updatedUserInfo)
            }

            XLog.d("设备ID刷新完成: $newDeviceId")
            newDeviceId
        } catch (e: Exception) {
            XLog.e("刷新设备ID失败", e)
            getDeviceId() // 返回当前设备ID
        }
    }

    /**
     * 检查用户是否已登录
     * @return 是否已登录
     */
    fun isUserLoggedIn(): Boolean {
        return try {
            val userInfo = getCurrentUserInfo()
            val isLoggedIn = userInfo?.isValidLogin() ?: false
            isLoggedIn
        } catch (e: Exception) {
            XLog.e("检查用户登录状态失败", e)
            false
        }
    }

    /**
     * 设置用户登录状态
     * @param loggedIn 登录状态
     * @param userId 用户ID（可选）
     * @param userName 用户名（可选）
     */
    fun setUserLoggedIn(loggedIn: Boolean, userId: String? = null, userName: String? = null) {
        try {
            val currentInfo = getCurrentUserInfo() ?: UserInfo.createDefault(getDeviceId())
            val updatedInfo = if (loggedIn) {
                currentInfo.updateLoginStatus(true).copy(
                    userId = userId ?: currentInfo.userId,
                    userName = userName ?: currentInfo.userName
                )
            } else {
                currentInfo.updateLoginStatus(false)
            }

            saveUserInfo(updatedInfo)

            // 通知登录状态变化
            notifyLoginStatusChanged(loggedIn)

            XLog.d("用户登录状态更新: $loggedIn")
        } catch (e: Exception) {
            XLog.e("设置用户登录状态失败", e)
        }
    }

    /**
     * 获取用户登录时间
     * @return 登录时间戳，未登录返回null
     */
    fun getUserLoginTime(): Long? {
        return try {
            getCurrentUserInfo()?.loginTime
        } catch (e: Exception) {
            XLog.e("获取用户登录时间失败", e)
            null
        }
    }

    /**
     * 获取当前用户信息
     * @return 用户信息，如果不存在返回null
     */
    fun getUserInfo(): UserInfo? {
        return getCurrentUserInfo()
    }

    /**
     * 保存用户信息
     * @param userInfo 用户信息
     */
    fun saveUserInfo(userInfo: UserInfo) {
        try {
            // 更新缓存
            currentUserInfo = userInfo
            XLog.d("用户信息保存成功")
        } catch (e: Exception) {
            XLog.e("保存用户信息失败", e)
        }
    }

    /**
     * 清除用户信息
     */
    fun clearUserInfo() {
        try {
            currentUserInfo = null
            XLog.d("用户信息清除完成")
        } catch (e: Exception) {
            XLog.e("清除用户信息失败", e)
        }
    }

    /**
     * 调用服务器退出登录接口
     * @return 是否调用成功（不管服务器返回什么都算成功，只要网络请求发出去了）
     */
    suspend fun logoutFromServer(): Boolean {
        return try {
            val userId = currentClientLoginData?.id
            if (userId.isNullOrEmpty()) {
                XLog.w("用户ID为空，跳过服务器退出登录调用")
                return true
            }

            XLog.d("调用服务器退出登录接口，userId: $userId")

            val httpClient = com.hwb.timecontroller.network.NetworkManager.httpClient
            val fullUrl = com.hwb.timecontroller.network.NetworkConfig.BASE_URL +
                         com.hwb.timecontroller.network.NetworkConfig.BASE_PATH + "/" +
                         com.hwb.timecontroller.network.NetworkConfig.ENDPOINT_LOGOUT

            val response = httpClient.submitForm(
                url = fullUrl,
                formParameters = io.ktor.http.parameters {
                    append("mechine_id", "1")
                    append("owner_id", "1")
                    append("userId", userId)
                }
            )

            val responseBody = response.body<com.hwb.timecontroller.network.LogoutResponse>()
            XLog.d("服务器退出登录响应: code=${responseBody.code}, msg=${responseBody.msg}")

            // 不管服务器返回什么都算成功，因为要求就算接口报错也要做本地退出
            true

        } catch (e: Exception) {
            XLog.w("调用服务器退出登录接口失败，继续执行本地退出", e)
            // 网络异常也算成功，因为要求就算接口报错也要做本地退出
            true
        }
    }

    /**
     * 用户退出登录
     * 先调用服务器接口，然后清除本地用户信息和登录状态
     */
    suspend fun logout() {
        try {
            XLog.d("用户退出登录")

            // 先调用服务器退出登录接口
            logoutFromServer()

            // 设置登录状态为false
            setUserLoggedIn(false)

            // 清除用户信息缓存
            currentUserInfo = null

            // 清除完整的客户端登录数据缓存
            currentClientLoginData = null

            // 重置随机码
            resetRandomCode()

            // 停止倒计时服务（服务销毁时会自动重置CountdownManager状态）
            try {
                val context = appContext
                if (context != null) {
                    val intent = android.content.Intent(
                        context,
                        com.hwb.timecontroller.service.CountdownService::class.java
                    )
                    context.stopService(intent)
                    XLog.d("已停止倒计时服务")
                } else {
                    XLog.w("应用上下文为空，无法停止倒计时服务")
                }
            } catch (e: Exception) {
                XLog.w("停止倒计时服务失败", e)
            }

            XLog.d("用户退出登录完成")
        } catch (e: Exception) {
            XLog.e("用户退出登录失败", e)
        }
    }

    /**
     * 设置完整的客户端登录数据（内存中，不持久化）
     * @param clientData 客户端登录数据
     */
    fun setClientLoginData(clientData: com.hwb.timecontroller.network.ClientLoginData?) {
        try {
            currentClientLoginData = clientData
            XLog.d("设置客户端登录数据: ${clientData?.userName}")
        } catch (e: Exception) {
            XLog.e("设置客户端登录数据失败", e)
        }
    }

    /**
     * 获取完整的客户端登录数据
     * @return 客户端登录数据，可能为null
     */
    fun getClientLoginData(): com.hwb.timecontroller.network.ClientLoginData? {
        return currentClientLoginData
    }

    /**
     * 添加用户登录状态变化监听器
     * @param listener 监听器
     */
    fun addLoginStatusListener(listener: UserLoginStatusListener) {
        loginStatusListeners.add(listener)
        XLog.d("添加用户登录状态监听器")
    }

    /**
     * 移除用户登录状态变化监听器
     * @param listener 监听器
     */
    fun removeLoginStatusListener(listener: UserLoginStatusListener) {
        loginStatusListeners.remove(listener)
        XLog.d("移除用户登录状态监听器")
    }

    /**
     * 通知用户登录状态变化
     * @param isLoggedIn 登录状态
     */
    private fun notifyLoginStatusChanged(isLoggedIn: Boolean) {
        try {
            loginStatusListeners.forEach { listener ->
                try {
                    listener.onLoginStatusChanged(isLoggedIn)
                } catch (e: Exception) {
                    XLog.e("通知登录状态变化失败", e)
                }
            }
        } catch (e: Exception) {
            XLog.e("通知登录状态变化异常", e)
        }
    }

    /**
     * 更新用户最后活跃时间
     */
    fun updateUserActiveTime() {
        try {
            val currentInfo = getCurrentUserInfo()
            if (currentInfo != null) {
                val lastUpdate = currentInfo.lastActiveTime ?: 0L
                val now = System.currentTimeMillis()

                // 避免频繁更新，只有超过配置的间隔才更新
                if (now - lastUpdate > UserStorageKeys.Config.USER_ACTIVE_UPDATE_INTERVAL_MS) {
                    val updatedInfo = currentInfo.updateLastActiveTime()
                    saveUserInfo(updatedInfo)
                    XLog.d("用户活跃时间更新")
                }
            }
        } catch (e: Exception) {
            XLog.e("更新用户活跃时间失败", e)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        try {
            // 清理余额轮询管理器（包含扣款功能）
            BalancePollingManager.cleanup()

            managerScope.cancel()
            currentUserInfo = null
            cachedDeviceId = null
            appContext = null
            XLog.d("UserManager资源清理完成")
        } catch (e: Exception) {
            XLog.e("UserManager资源清理失败", e)
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 获取当前用户信息（内部方法）
     */
    private fun getCurrentUserInfo(): UserInfo? {
        // 优先返回缓存
        currentUserInfo?.let { return it }

        return try {
            currentUserInfo
        } catch (e: Exception) {
            XLog.e("获取当前用户信息失败", e)
            null
        }
    }


    /**
     * 生成并保存新的设备ID
     */
    private fun generateAndSaveDeviceId(): String {
        val newDeviceId = try {
            val uuid = UuidCreator.getTimeOrderedEpoch()
            val uuidString = uuid.toString()
            XLog.d("生成UUIDv7: $uuidString")
            // 使用MD5处理UUID，缩短位数到32位
            val md5Hash = generateMd5Hash(uuidString)
            XLog.d("MD5处理后的设备ID: $md5Hash")
            md5Hash
        } catch (e: Exception) {
            XLog.e("生成UUIDv7失败，使用随机UUID作为备选", e)
            val fallbackUuid = UUID.randomUUID().toString()
            generateMd5Hash(fallbackUuid)
        }
        val currentTime = System.currentTimeMillis()

        // 保存到MMKV
        mmkv.putString(UserStorageKeys.MMKV.USER_DEVICE_UUID, newDeviceId)
        mmkv.putLong(UserStorageKeys.MMKV.DEVICE_UUID_GENERATED_TIME, currentTime)

        // 异步备份到MediaStore
        managerScope.launch {
            try {
                val backupSuccess = saveDeviceIdToMediaStore(newDeviceId)
                mmkv.putBoolean(UserStorageKeys.MMKV.DEVICE_UUID_BACKUP_STATUS, backupSuccess)
                XLog.d("设备ID MediaStore备份${if (backupSuccess) "成功" else "失败"}")
            } catch (e: Exception) {
                XLog.e("设备ID MediaStore备份异常", e)
                mmkv.putBoolean(UserStorageKeys.MMKV.DEVICE_UUID_BACKUP_STATUS, false)
            }
        }

        return newDeviceId
    }

    /**
     * 生成MD5哈希值（16位）
     * @param input 输入字符串
     * @return MD5哈希值的16位字符串
     */
    private fun generateMd5Hash(input: String): String {
        return try {
            val md = MessageDigest.getInstance("MD5")
            val digest = md.digest(input.toByteArray())
            val hexString = StringBuilder()
            for (byte in digest) {
                val hex = Integer.toHexString(0xff and byte.toInt())
                if (hex.length == 1) {
                    hexString.append('0')
                }
                hexString.append(hex)
            }
            // 取前16位
            hexString.toString().substring(0, 16)
        } catch (e: Exception) {
            XLog.e("生成MD5哈希失败", e)
            // 如果MD5失败，返回输入字符串的前16位（如果长度足够）
            if (input.length >= 16) {
                input.substring(0, 16)
            } else {
                input.padEnd(16, '0')
            }
        }
    }

    /**
     * 从MediaStore读取设备ID
     */
    private fun readDeviceIdFromMediaStore(): String? {
        val context = appContext ?: return null

        return try {
            // Android 10+ 使用MediaStore API
            readDeviceIdFromMediaStoreQ(context)
        } catch (e: Exception) {
            XLog.e("从MediaStore读取设备ID失败", e)
            null
        }
    }

    /**
     * 保存设备ID到MediaStore
     */
    private suspend fun saveDeviceIdToMediaStore(deviceId: String): Boolean =
        withContext(Dispatchers.IO) {
            val context = appContext ?: return@withContext false

            return@withContext try {
                // Android 10+ 使用MediaStore API
                saveDeviceIdToMediaStoreQ(context, deviceId)
            } catch (e: Exception) {
                XLog.e("保存设备ID到MediaStore失败", e)
                false
            }
        }

    /**
     * Android 10+ 从MediaStore读取设备ID
     */
    private fun readDeviceIdFromMediaStoreQ(context: Context): String? {
        return try {
            val projection = arrayOf(
                MediaStore.Files.FileColumns._ID,
                MediaStore.Files.FileColumns.DISPLAY_NAME,
                MediaStore.Files.FileColumns.RELATIVE_PATH
            )

            // 修复查询条件：同时检查文件名和路径
            val targetPath = Environment.DIRECTORY_DOCUMENTS + "/TimeController/"
            val selection =
                "${MediaStore.Files.FileColumns.DISPLAY_NAME} = ? AND ${MediaStore.Files.FileColumns.RELATIVE_PATH} = ?"
            val selectionArgs =
                arrayOf(UserStorageKeys.MediaStore.DEVICE_UUID_FILE_NAME, targetPath)

            context.contentResolver.query(
                MediaStore.Files.getContentUri("external"),
                projection,
                selection,
                selectionArgs,
                null
            )?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
                    val id = cursor.getLong(idColumn)
                    val uri = MediaStore.Files.getContentUri("external", id)

                    context.contentResolver.openInputStream(uri)?.use { inputStream ->
                        inputStream.bufferedReader().readText().trim()
                    }
                } else {
                    XLog.d("MediaStore中未找到指定路径下的设备ID文件")
                    null
                }
            }
        } catch (e: Exception) {
            XLog.e("从MediaStore读取设备ID异常", e)
            null
        }
    }

    /**
     * Android 10+ 保存设备ID到MediaStore
     */
    private fun saveDeviceIdToMediaStoreQ(context: Context, deviceId: String): Boolean {
        return try {
            // 先检查文件是否已存在
            val targetPath = Environment.DIRECTORY_DOCUMENTS + "/TimeController/"
            val projection = arrayOf(MediaStore.Files.FileColumns._ID)
            val selection =
                "${MediaStore.Files.FileColumns.DISPLAY_NAME} = ? AND ${MediaStore.Files.FileColumns.RELATIVE_PATH} = ?"
            val selectionArgs =
                arrayOf(UserStorageKeys.MediaStore.DEVICE_UUID_FILE_NAME, targetPath)

            context.contentResolver.query(
                MediaStore.Files.getContentUri("external"),
                projection,
                selection,
                selectionArgs,
                null
            )?.use { cursor ->
                if (cursor.moveToFirst()) {
                    // 文件已存在，更新内容
                    val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Files.FileColumns._ID)
                    val id = cursor.getLong(idColumn)
                    val uri = MediaStore.Files.getContentUri("external", id)

                    context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                        outputStream.write(deviceId.toByteArray())
                        outputStream.flush()
                    }
                    XLog.d("设备ID更新到MediaStore成功")
                    return true
                }
            }

            // 文件不存在，创建新文件
            val contentValues = ContentValues().apply {
                put(
                    MediaStore.Files.FileColumns.DISPLAY_NAME,
                    UserStorageKeys.MediaStore.DEVICE_UUID_FILE_NAME
                )
                put(
                    MediaStore.Files.FileColumns.MIME_TYPE,
                    UserStorageKeys.MediaStore.DEVICE_UUID_FILE_MIME_TYPE
                )
                put(
                    MediaStore.Files.FileColumns.RELATIVE_PATH,
                    Environment.DIRECTORY_DOCUMENTS + "/TimeController"
                )
            }

            val uri = context.contentResolver.insert(
                MediaStore.Files.getContentUri("external"),
                contentValues
            )

            if (uri != null) {
                context.contentResolver.openOutputStream(uri)?.use { outputStream ->
                    outputStream.write(deviceId.toByteArray())
                    outputStream.flush()
                }
                XLog
                    .d("设备ID保存到MediaStore成功")
                true
            } else {
                XLog.e("创建MediaStore文件失败")
                false
            }
        } catch (e: Exception) {
            XLog.e("保存设备ID到MediaStore异常", e)
            false
        }
    }

    /**
     * Android 9及以下从传统存储读取设备ID
     */
    private fun readDeviceIdFromLegacyStorage(): String? {
        return try {
            val documentsDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            val appDir = File(documentsDir, "TimeController")
            val uuidFile = File(appDir, UserStorageKeys.MediaStore.DEVICE_UUID_FILE_NAME)

            if (uuidFile.exists() && uuidFile.canRead()) {
                uuidFile.readText().trim()
            } else {
                XLog
                    .d("传统存储中未找到设备ID文件")
                null
            }
        } catch (e: Exception) {
            XLog.e("从传统存储读取设备ID异常", e)
            null
        }
    }

    /**
     * Android 9及以下保存设备ID到传统存储
     */
    private fun saveDeviceIdToLegacyStorage(deviceId: String): Boolean {
        return try {
            val documentsDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            val appDir = File(documentsDir, "TimeController")

            // 确保目录存在
            if (!appDir.exists()) {
                appDir.mkdirs()
            }

            val uuidFile = File(appDir, UserStorageKeys.MediaStore.DEVICE_UUID_FILE_NAME)
            FileOutputStream(uuidFile).use { outputStream ->
                outputStream.write(deviceId.toByteArray())
                outputStream.flush()
            }

            XLog.d("设备ID保存到传统存储成功")
            true
        } catch (e: Exception) {
            XLog.e("保存设备ID到传统存储异常", e)
            false
        }
    }
}
