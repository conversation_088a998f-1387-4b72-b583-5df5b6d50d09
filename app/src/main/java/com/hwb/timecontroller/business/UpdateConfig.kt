package com.hwb.timecontroller.business

import com.tencent.mmkv.MMKV
import kotlinx.serialization.builtins.ListSerializer
import kotlinx.serialization.json.Json.Default.decodeFromString
import com.elvishew.xlog.XLog

/**
 * 更新配置管理器
 * 管理更新相关的配置和历史记录
 */
object UpdateConfig {
    
    private const val KEY_LAST_CHECK_TIME = "update_last_check_time"
    private const val KEY_IGNORED_VERSION = "update_ignored_version"
    private const val KEY_AUTO_CHECK_ENABLED = "update_auto_check_enabled"
    private const val KEY_WIFI_ONLY = "update_wifi_only"
    private const val KEY_UPDATE_HISTORY = "update_history"
    
    private val mmkv: MMKV by lazy {
        MMKV.mmkvWithID("update_config")
    }
    
    /**
     * 获取上次检查更新的时间
     */
    fun getLastCheckTime(): Long {
        return mmkv.decodeLong(KEY_LAST_CHECK_TIME, 0L)
    }
    
    /**
     * 设置上次检查更新的时间
     */
    fun setLastCheckTime(time: Long) {
        mmkv.encode(KEY_LAST_CHECK_TIME, time)
    }
    
    /**
     * 获取被忽略的版本
     */
    fun getIgnoredVersion(): String? {
        return mmkv.decodeString(KEY_IGNORED_VERSION, null)
    }
    
    /**
     * 设置忽略的版本
     */
    fun setIgnoredVersion(version: String?) {
        if (version != null) {
            mmkv.encode(KEY_IGNORED_VERSION, version)
        } else {
            mmkv.removeValueForKey(KEY_IGNORED_VERSION)
        }
    }
    
    /**
     * 是否启用自动检查更新
     */
    fun isAutoCheckEnabled(): Boolean {
        return mmkv.decodeBool(KEY_AUTO_CHECK_ENABLED, true)
    }
    
    /**
     * 设置是否启用自动检查更新
     */
    fun setAutoCheckEnabled(enabled: Boolean) {
        mmkv.encode(KEY_AUTO_CHECK_ENABLED, enabled)
    }
    
    /**
     * 是否仅在WiFi下检查更新
     */
    fun isWifiOnly(): Boolean {
        return mmkv.decodeBool(KEY_WIFI_ONLY, false)
    }
    
    /**
     * 设置是否仅在WiFi下检查更新
     */
    fun setWifiOnly(wifiOnly: Boolean) {
        mmkv.encode(KEY_WIFI_ONLY, wifiOnly)
    }
    
    /**
     * 记录更新历史
     */
    fun recordUpdateHistory(version: String, updateTime: Long, success: Boolean) {
        try {
            val history = getUpdateHistory().toMutableList()
            val record = UpdateHistoryRecord(
                version = version,
                updateTime = updateTime,
                success = success,
                timestamp = System.currentTimeMillis()
            )
            
            history.add(0, record) // 添加到列表开头
            
            // 只保留最近10条记录
            if (history.size > 10) {
                history.removeAt(history.size - 1)
            }
            
            val historyJson = kotlinx.serialization.json.Json.encodeToString(
                kotlinx.serialization.builtins.ListSerializer(UpdateHistoryRecord.serializer()),
                history
            )
            mmkv.encode(KEY_UPDATE_HISTORY, historyJson)
            
        } catch (e: Exception) {
            XLog.e("记录更新历史失败", e)
        }
    }
    
    /**
     * 获取更新历史记录
     */
    fun getUpdateHistory(): List<UpdateHistoryRecord> {
        return try {
            val historyJson = mmkv.decodeString(KEY_UPDATE_HISTORY, "[]")
            decodeFromString(
                ListSerializer(UpdateHistoryRecord.serializer()),
                historyJson!!
            )
        } catch (e: Exception) {
            XLog.e("获取更新历史失败", e)
            emptyList()
        }
    }
    
    /**
     * 清除更新历史
     */
    fun clearUpdateHistory() {
        mmkv.removeValueForKey(KEY_UPDATE_HISTORY)
    }
    
    /**
     * 检查是否应该进行自动检查
     * @param intervalHours 检查间隔（小时）
     */
    fun shouldAutoCheck(intervalHours: Int = 24): Boolean {
        if (!isAutoCheckEnabled()) {
            return false
        }
        
        val lastCheckTime = getLastCheckTime()
        val currentTime = System.currentTimeMillis()
        val intervalMs = intervalHours * 60 * 60 * 1000L
        
        return (currentTime - lastCheckTime) >= intervalMs
    }
}

/**
 * 更新历史记录数据类
 */
@kotlinx.serialization.Serializable
data class UpdateHistoryRecord(
    val version: String,
    val updateTime: Long,
    val success: Boolean,
    val timestamp: Long
)
