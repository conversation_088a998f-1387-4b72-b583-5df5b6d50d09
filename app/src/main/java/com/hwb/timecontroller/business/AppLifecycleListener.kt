package com.hwb.timecontroller.business

/**
 * 应用生命周期监听器接口
 * 用于监听前台应用变化事件
 */
interface AppLifecycleListener {
    
    /**
     * 前台应用发生变化时调用
     * @param packageName 当前前台应用的包名
     * @param className 当前前台应用的Activity类名
     */
    fun onForegroundAppChanged(packageName: String, className: String)
    
    /**
     * 应用启动被拦截时调用
     * @param packageName 被拦截的应用包名
     * @param className 被拦截的Activity类名
     */
    fun onAppLaunchIntercepted(packageName: String, className: String) {
        // 默认空实现，子类可选择性重写
    }
    
    /**
     * 应用被阻止时调用
     * @param packageName 被阻止的应用包名
     * @param reason 阻止原因
     */
    fun onAppBlocked(packageName: String, reason: String) {
        // 默认空实现，子类可选择性重写
    }
}

/**
 * 自己应用可见性监听器接口
 * 用于监听自己应用的Activity可见性变化
 */
interface OwnAppVisibilityListener {

    /**
     * 自己应用可见性发生变化时调用
     * @param isVisible 是否可见
     * @param activityClassName 相关的Activity类名
     */
    fun onOwnAppVisibilityChanged(isVisible: Boolean, activityClassName: String)
}
