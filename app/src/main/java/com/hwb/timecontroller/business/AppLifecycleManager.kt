package com.hwb.timecontroller.business

import com.elvishew.xlog.XLog
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 应用生命周期管理器
 * 统一管理应用切换事件，并通知所有监听器
 * 使用单例模式，线程安全
 */
object AppLifecycleManager {

    // 使用线程安全的List存储监听器
    private val listeners = CopyOnWriteArrayList<AppLifecycleListener>()

    // 当前前台应用包名
    private var currentForegroundApp: String? = null

    // 当前前台应用类名
    private var currentForegroundClass: String? = null

    // 上一个前台应用包名
    private var previousForegroundApp: String? = null

    // 上一个前台应用类名
    private var previousForegroundClass: String? = null

    // 自己应用的Activity状态管理
    private var currentOwnActivity: String? = null // 当前可见的自己应用的Activity
    private var isOwnAppVisible: Boolean = false // 自己应用是否有Activity可见
    
    /**
     * 获取当前前台应用包名
     */
    fun getCurrentForegroundApp(): String? = currentForegroundApp

    /**
     * 获取当前前台应用类名
     */
    fun getCurrentForegroundClass(): String? = currentForegroundClass

    /**
     * 获取上一个前台应用包名
     */
    fun getPreviousForegroundApp(): String? = previousForegroundApp

    /**
     * 获取上一个前台应用类名
     */
    fun getPreviousForegroundClass(): String? = previousForegroundClass

    /**
     * 获取当前自己应用的可见Activity
     */
    fun getCurrentOwnActivity(): String? = currentOwnActivity

    /**
     * 检查自己应用是否有Activity可见
     */
    fun isOwnAppVisible(): Boolean = isOwnAppVisible
    
    /**
     * 添加应用生命周期监听器
     * @param listener 监听器实例
     */
    fun addListener(listener: AppLifecycleListener) {
        if (!listeners.contains(listener)) {
            listeners.add(listener)
            XLog.d("添加应用生命周期监听器: ${listener.javaClass.simpleName}, 当前监听器数量: ${listeners.size}")

            // 如果当前有前台应用，立即通知新监听器
            currentForegroundApp?.let { packageName ->
                val className = currentForegroundClass ?: ""
                try {
                    XLog.d("通知新监听器当前前台应用: $packageName - $className")
                    listener.onForegroundAppChanged(packageName, className)
                } catch (e: Exception) {
                    XLog.e("通知新监听器当前前台应用失败: ${listener.javaClass.simpleName}", e)
                }
            } ?: run {
                XLog.d("当前无前台应用信息，新监听器将等待首次应用切换事件")
            }
        } else {
            XLog.w("监听器已存在，跳过重复添加: ${listener.javaClass.simpleName}")
        }
    }
    
    /**
     * 移除应用生命周期监听器
     * @param listener 监听器实例
     */
    fun removeListener(listener: AppLifecycleListener) {
        if (listeners.remove(listener)) {
            XLog.d("移除应用生命周期监听器: ${listener.javaClass.simpleName}")
        }
    }
    
    /**
     * 应用切换事件
     * 由无障碍服务调用
     * @param packageName 当前前台应用包名
     * @param className 当前前台应用Activity类名
     */
    fun onAppSwitched(packageName: String, className: String = "") {
        try {
            val previousApp = currentForegroundApp
            val previousClass = currentForegroundClass

            // 如果应用和类名都没有变化，不处理
            if (packageName == previousApp && className == previousClass) {
                return
            }

            // 更新状态
            previousForegroundApp = previousApp
            previousForegroundClass = previousClass
            currentForegroundApp = packageName
            currentForegroundClass = className

            XLog.d("应用切换: $previousApp($previousClass) -> $packageName($className)")

            // 通知所有监听器
            notifyForegroundAppChanged(packageName, className)

        } catch (e: Exception) {
            XLog.e("处理应用切换事件失败", e)
        }
    }
    
    /**
     * 应用启动拦截事件
     * @param packageName 被拦截的应用包名
     * @param className 被拦截的Activity类名
     */
    fun onAppLaunchIntercepted(packageName: String, className: String) {
        try {
            XLog.d("应用启动被拦截: $packageName - $className")
            
            // 通知所有监听器
            listeners.forEach { listener ->
                try {
                    listener.onAppLaunchIntercepted(packageName, className)
                } catch (e: Exception) {
                    XLog.e("通知监听器应用启动拦截失败: ${listener.javaClass.simpleName}", e)
                }
            }
            
        } catch (e: Exception) {
            XLog.e("处理应用启动拦截事件失败", e)
        }
    }
    
    /**
     * 应用被阻止事件
     * @param packageName 被阻止的应用包名
     * @param reason 阻止原因
     */
    fun onAppBlocked(packageName: String, reason: String) {
        try {
            XLog.d("应用被阻止: $packageName, 原因: $reason")
            
            // 通知所有监听器
            listeners.forEach { listener ->
                try {
                    listener.onAppBlocked(packageName, reason)
                } catch (e: Exception) {
                    XLog.e("通知监听器应用阻止失败: ${listener.javaClass.simpleName}", e)
                }
            }
            
        } catch (e: Exception) {
            XLog.e("处理应用阻止事件失败", e)
        }
    }



    /**
     * 通知所有监听器前台应用变化
     */
    private fun notifyForegroundAppChanged(packageName: String, className: String) {
        listeners.forEach { listener ->
            try {
                listener.onForegroundAppChanged(packageName, className)
            } catch (e: Exception) {
                XLog.e("通知监听器前台应用变化失败: ${listener.javaClass.simpleName}", e)
            }
        }
    }
    
    /**
     * 自己应用Activity变为可见
     * @param activityClassName Activity类名
     */
    fun onOwnActivityResumed(activityClassName: String) {
        try {
            val wasVisible = isOwnAppVisible
            currentOwnActivity = activityClassName
            isOwnAppVisible = true

            XLog.d("自己应用Activity变为可见: $activityClassName")

            // 如果之前不可见，现在变为可见，通知监听器
            if (!wasVisible) {
                notifyOwnAppVisibilityChanged(true, activityClassName)
            }

        } catch (e: Exception) {
            XLog.e("处理自己应用Activity可见事件失败", e)
        }
    }

    /**
     * 自己应用Activity变为不可见
     * @param activityClassName Activity类名
     */
    fun onOwnActivityPaused(activityClassName: String) {
        try {
            XLog.d("自己应用Activity变为不可见: $activityClassName")

            // 如果暂停的是当前可见的Activity，更新状态
            if (currentOwnActivity == activityClassName) {
                currentOwnActivity = null
                isOwnAppVisible = false

                // 通知监听器应用变为不可见
                notifyOwnAppVisibilityChanged(false, activityClassName)
            }

        } catch (e: Exception) {
            XLog.e("处理自己应用Activity不可见事件失败", e)
        }
    }

    /**
     * 通知监听器自己应用可见性变化
     */
    private fun notifyOwnAppVisibilityChanged(isVisible: Boolean, activityClassName: String) {
        listeners.forEach { listener ->
            try {
                if (listener is OwnAppVisibilityListener) {
                    listener.onOwnAppVisibilityChanged(isVisible, activityClassName)
                }
            } catch (e: Exception) {
                XLog.e("通知监听器自己应用可见性变化失败: ${listener.javaClass.simpleName}", e)
            }
        }
    }

    /**
     * 获取当前监听器数量（用于调试）
     */
    fun getListenerCount(): Int = listeners.size

    /**
     * 清空所有监听器（用于测试或重置）
     */
    fun clearAllListeners() {
        listeners.clear()
        currentOwnActivity = null
        isOwnAppVisible = false
        XLog.d("清空所有应用生命周期监听器")
    }
}
