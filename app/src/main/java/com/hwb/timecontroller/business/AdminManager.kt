package com.hwb.timecontroller.business

import android.content.Context
import android.content.Intent
import android.text.InputType
import com.hjq.toast.Toaster
import com.hwb.timecontroller.MyApplication
import com.hwb.timecontroller.activity.EmptyActivity
import com.hwb.timecontroller.activity.PasswordDialogActivity
import com.hwb.timecontroller.business.LockManager.LockData
import com.hwb.timecontroller.constant.Constant
import com.kongzue.dialogx.dialogs.InputDialog
import com.kongzue.dialogx.dialogs.MessageDialog
import com.kongzue.dialogx.interfaces.DialogLifecycleCallback
import com.kongzue.dialogx.util.InputInfo
import com.elvishew.xlog.XLog

/**
 * 管理员管理
 * <p>
 * Author:huangwubin
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/2: First created this class.
 */
object AdminManager {

    // 管控状态内存变量（不再持久化存储）
    private var _isGovernanceState: Boolean = false

    /**
     * 获取管控状态
     */
    val isGovernanceState: Boolean
        get() = _isGovernanceState

    /**
     * 改变管控状态
     */
    fun changeGovernanceState(state: Boolean) {
        try {
            _isGovernanceState = state
            XLog.d("管控状态已更新: $state")
        } catch (e: Exception) {
            XLog.e("更新管控状态失败", e)
        }
    }

    /**
     * 启动时同步管控状态
     * 根据实际情况设置管控状态
     */
    fun syncGovernanceState(context: Context) {
        try {
            val actualInControl = checkActualControlState(context)
            _isGovernanceState = actualInControl
            XLog.d("同步管控状态完成: $actualInControl")
        } catch (e: Exception) {
            XLog.e("同步管控状态失败", e)
            _isGovernanceState = false
        }
    }

    /**
     * 检查实际管控状态
     * 判断homePackage是否在前台且系统是否在LockTask模式
     */
    private fun checkActualControlState(context: Context): Boolean {
        return try {
            // 检查homePackage是否在前台
            val currentForegroundApp = AppLifecycleManager.getCurrentForegroundApp()
            val isHomePackageInForeground = currentForegroundApp == Constant.homePackage

            // 检查系统是否在LockTask模式
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val isInLockTaskMode = activityManager.isInLockTaskMode

            val actualInControl = isHomePackageInForeground && isInLockTaskMode
            XLog.d("实际管控状态检查 - homePackage在前台: $isHomePackageInForeground, LockTask模式: $isInLockTaskMode, 实际管控: $actualInControl")

            actualInControl
        } catch (e: Exception) {
            XLog.e("检查实际管控状态失败", e)
            false
        }
    }

    /**
     * 检查管理员密码 - 使用 Activity 模式（用于 Service 或特殊场景）
     * 这种方式避免了 DialogX 在透明 Activity 中的软键盘问题
     */
    fun checkWithActivity(context: Context) {
        val intent = Intent(context, PasswordDialogActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(intent)
    }

    fun restartApp(){
        val context = MyApplication.myApp
        val intent = Intent(context, EmptyActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        context.startActivity(intent)
    }
}