package com.hwb.timecontroller.business

import android.app.ActivityManager
import android.app.ActivityOptions
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import com.hjq.toast.Toaster
import com.hwb.timecontroller.AppDeviceAdminReceiver
import com.hwb.timecontroller.MyApplication
import com.hwb.timecontroller.constant.Constant
import com.hwb.timecontroller.network.NetworkConfig
import com.hwb.timecontroller.network.NetworkManager
import com.hwb.timecontroller.network.RemoteAppInfo
import com.hwb.timecontroller.network.RemoteWhitelistResponse
import com.tencent.mmkv.MMKV
import io.ktor.client.call.*
import io.ktor.client.request.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import com.elvishew.xlog.XLog

/**
 * 白名单管理类
 * <p>
 * Author:huangwubin
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/1: First created this class.
 */
object WhitelistManager {
    /// 自定义白名单
    private const val KEY_WHITELIST = "customer_whitelist_packages"

    /// 远程白名单更新时间记录（内存存储）
    private var lastRemoteWhitelistUpdateTime: Long = 0L

    // 无条件放行
    private val UNCONDITIONAL_WHITELIST = mutableSetOf<String>(
        "com.google.android.inputmethod.latin",//输入法
        "com.android.inputmethod.latin",//模拟器输入法
        Constant.homePackage,
    ).apply {
        addAll(Constant.defPackage)
        add(MyApplication.myApp.packageName)
    }

    // 默认系统白名单应用
    private val DEFAULT_SYSTEM_WHITELIST = mutableSetOf(
        "com.google.android.inputmethod.latin",//输入法
    ).apply {
        addAll(UNCONDITIONAL_WHITELIST)
    }

    /**
     * 获取所有白名单应用包名
     */
    fun getWhitelistPackages(): Set<String> {
        val userWhitelist = MMKV.defaultMMKV()
            .getStringSet(KEY_WHITELIST, emptySet<String>()) ?: emptySet<String>()

        return DEFAULT_SYSTEM_WHITELIST + userWhitelist
    }

    /**
     * 添加应用到白名单
     */
    fun addToWhitelist(packageName: String, context: Context? = null) {
        val currentWhitelist = getUserWhitelistPackages().toMutableSet()
        currentWhitelist.add(packageName)
        saveUserWhitelist(currentWhitelist)
        // 同步更新 LockTask 白名单
        updateLockTaskPackages(context)
    }

    /**
     * 从白名单移除应用
     */
    fun removeFromWhitelist(packageName: String, context: Context? = null) {
        // 不能移除系统默认白名单
        if (DEFAULT_SYSTEM_WHITELIST.contains(packageName)) {
            return
        }

        val currentWhitelist = getUserWhitelistPackages().toMutableSet()
        currentWhitelist.remove(packageName)
        saveUserWhitelist(currentWhitelist)
        // 同步更新 LockTask 白名单
        updateLockTaskPackages(context)
    }

    /**
     * 检查应用是否在白名单中
     */
    fun isInWhitelist(packageName: String): Boolean {
        return getWhitelistPackages().contains(packageName)
    }

    /**
     * 检查应用是否在无条件白名单中
     */
    fun isInUnconditionalWhitelist(packageName: String): Boolean {
        return UNCONDITIONAL_WHITELIST.contains(packageName)
    }

    /**
     * 检查应用是否在白名单中（别名方法，用于应用启动控制）
     */
    fun isAppInWhitelist(packageName: String): Boolean {
        return isInWhitelist(packageName)
    }

    /**
     * 获取用户自定义的白名单（不包括系统默认）
     */
    fun getUserWhitelistPackages(): Set<String> {
        return MMKV.defaultMMKV().getStringSet(KEY_WHITELIST, null) ?: emptySet()
    }

    /**
     * 保存用户白名单
     */
    private fun saveUserWhitelist(whitelist: Set<String>) {
        MMKV.defaultMMKV().putStringSet(
            KEY_WHITELIST, whitelist.filter {
                it.isNotBlank()
            }.toSet()
        )
    }

    /**
     * 获取所有已安装的应用信息
     */
    fun getAllInstalledApps(context: Context): List<AppInfo> {
        val packageManager = context.packageManager
        val installedApps = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)

        return installedApps
            .filter { it.flags and ApplicationInfo.FLAG_SYSTEM == 0 } // 只显示用户安装的应用
            .filter { it.packageName != context.packageName } // 排除本应用
            .map { appInfo ->
                AppInfo(
                    packageName = appInfo.packageName,
                    appName = packageManager.getApplicationLabel(appInfo).toString(),
                    isInWhitelist = isInWhitelist(appInfo.packageName)
                )
            }
            .sortedBy { it.appName }
    }

    /**
     * 清空用户自定义白名单
     */
    fun clearUserWhitelist(context: Context? = null) {
        MMKV.defaultMMKV().remove(KEY_WHITELIST)
        // 同步更新 LockTask 白名单
        updateLockTaskPackages(context)
    }

    /**
     * 初始化白名单（应用启动时调用）
     * 同步设置 LockTask 白名单
     */
    fun initializeWhitelist(context: Context) {
        updateLockTaskPackages(context)
    }

    /**
     * 更新 LockTask 白名单
     * 将当前白名单中的所有包名设置到 setLockTaskPackages
     */
    fun updateLockTaskPackages(context: Context? = null) {
        try {
            // 如果没有传入 context，尝试从其他地方获取
            val appContext = context ?: getApplicationContext()
            if (appContext == null) {
                XLog.w("无法获取 Context，跳过 LockTask 白名单更新")
                return
            }

            val devicePolicyManager =
                appContext.getSystemService(Context.DEVICE_POLICY_SERVICE) as? DevicePolicyManager
            if (devicePolicyManager == null) {
                XLog.w("无法获取 DevicePolicyManager")
                return
            }

            val adminComponent = ComponentName(appContext, AppDeviceAdminReceiver::class.java)

            // 检查是否为设备所有者
            if (!devicePolicyManager.isDeviceOwnerApp(appContext.packageName)) {
                XLog.w("不是设备所有者，无法设置 LockTask 白名单")
                return
            }

            // 获取所有白名单包名
            val whitelistPackages = getWhitelistPackages()
            val lockTaskPackages = whitelistPackages.toTypedArray()

            // 设置 LockTask 白名单
            devicePolicyManager.setLockTaskPackages(adminComponent, lockTaskPackages)

            XLog.d("LockTask 白名单已更新，包含 ${lockTaskPackages.size} 个应用: ${lockTaskPackages.joinToString()}")

        } catch (e: Exception) {
            XLog.e("更新 LockTask 白名单失败", e)
        }
    }

    /**
     * 设置 LockTask 功能特性
     * 启用状态栏系统信息显示，包括电量、网络连接等图标
     */
    private fun setupLockTaskFeatures(devicePolicyManager: DevicePolicyManager, adminComponent: ComponentName) {
        try {
            // 启用状态栏系统信息区域显示
            // 这将显示电量、网络连接、声音等系统指示器
            val lockTaskFeatures = DevicePolicyManager.LOCK_TASK_FEATURE_SYSTEM_INFO

            devicePolicyManager.setLockTaskFeatures(adminComponent, lockTaskFeatures)

            XLog.d("LockTask 功能特性已设置：启用状态栏系统信息显示（电量、网络等）")

        } catch (e: Exception) {
            XLog.e("设置 LockTask 功能特性失败", e)
        }
    }

    /**
     * 获取应用上下文的辅助方法
     * 这是一个简单的实现，实际项目中可能需要更复杂的上下文管理
     */
    private fun getApplicationContext(): Context? {
        return try {
            // 这里需要一个静态的方式获取 Context
            // 可以通过 Application 单例或其他方式实现
            null // 暂时返回 null，需要调用方传入 context
        } catch (e: Exception) {
            null
        }
    }


    /**
     * 打开主页应用
     */
    fun openHomeApp() {
        try {
            val context = MyApplication.myApp

            // 确保设备所有者权限
            val devicePolicyManager = context.getSystemService(Context.DEVICE_POLICY_SERVICE) as? DevicePolicyManager
            val adminComponent = ComponentName(context, AppDeviceAdminReceiver::class.java)

            if (devicePolicyManager?.isDeviceOwnerApp(context.packageName) != true) {
                XLog.e("没有设备所有者权限，无法启动LockTask模式")
                Toaster.show("没有设备所有者权限")
                return
            }

            // 先更新白名单，确保homePackage在LockTask白名单中
            updateLockTaskPackages(context)

            // 设置 LockTask 功能特性，启用状态栏系统信息显示（包括电量图标）
            setupLockTaskFeatures(devicePolicyManager, adminComponent)

            val packageManager = context.packageManager
            val intent = packageManager.getLaunchIntentForPackage(Constant.homePackage)

            if (intent != null) {
                // 使用ActivityOptions强制启动LockTask模式
                val options = ActivityOptions.makeBasic()
                options.setLockTaskEnabled(true)

                // 添加必要的Intent标志
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)

                context.startActivity(intent, options.toBundle())
                XLog.d("以LockTask模式打开主页应用: ${Constant.homePackage}")

                // 延迟检查LockTask状态
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    checkLockTaskStatus(context)
                }, 2000) // 等待应用启动完成

                AdminManager.changeGovernanceState(true)
            } else {
                Toaster.show("未找到主页应用")
                XLog.w("未找到主页应用: ${Constant.homePackage}")
            }
        } catch (e: Exception) {
            XLog.e("打开主页应用失败", e)
            Toaster.show("打开主页应用失败")
        }
    }

    /**
     * 检查LockTask状态
     */
    private fun checkLockTaskStatus(context: Context) {
        try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val isInLockTask = activityManager.isInLockTaskMode
            val currentForegroundApp = AppLifecycleManager.getCurrentForegroundApp()

            XLog.d("LockTask状态检查 - 当前前台应用: $currentForegroundApp, LockTask模式: $isInLockTask")

            if (currentForegroundApp == Constant.homePackage) {
                if (isInLockTask) {
                    XLog.i("✅ homePackage成功进入LockTask模式")
                } else {
                    XLog.w("❌ homePackage未进入LockTask模式，可能需要其他方法")
                    // 可以在这里尝试其他方法或记录问题
                }
            } else {
                XLog.w("当前前台应用不是homePackage: $currentForegroundApp")
            }
        } catch (e: Exception) {
            XLog.e("检查LockTask状态时发生错误", e)
        }
    }

    /**
     * 异步拉取并更新远程白名单
     * 强制10分钟以上间隔，更新后自动同步LockTask白名单
     * @param context 上下文，用于更新LockTask白名单
     */
    fun fetchAndUpdateRemoteWhitelist(context: Context? = null) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 检查更新间隔
                if (!shouldUpdateRemoteWhitelist()) {
                    XLog.d("距离上次远程白名单更新不足10分钟，跳过本次更新")
                    return@launch
                }

                XLog.d("开始拉取远程白名单...")

                // 构建完整URL
                val fullUrl = NetworkConfig.REMOTE_WHITELIST_BASE_URL + NetworkConfig.ENDPOINT_REMOTE_WHITELIST

                // 创建专门用于HTTP请求的客户端
                val httpClient = NetworkManager.createHttpOnlyClient()

                try {
                    // 发起网络请求
                    val response = httpClient.get(fullUrl)

                    // 先获取原始文本，然后手动解析JSON
                    val responseText = response.body<String>()
                    XLog.d("远程白名单API原始响应: $responseText")

                    // 清除BOM字符和其他不可见字符
                    val cleanedText = responseText
                        .removePrefix("\uFEFF") // 移除UTF-8 BOM
                        .removePrefix("\uFFFE") // 移除UTF-16 BOM (little-endian)
                        .removePrefix("\uEFBBBF") // 移除UTF-8 BOM的另一种表示
                        .trim() // 移除首尾空白字符

                    XLog.d("清理后的JSON: $cleanedText")

                    // 手动解析JSON
                    val json = Json {
                        ignoreUnknownKeys = true
                        isLenient = true
                    }
                    val remoteApps: RemoteWhitelistResponse = json.decodeFromString(cleanedText)

                    XLog.d("成功获取远程白名单，包含 ${remoteApps.size} 个应用")

                    // 更新本地白名单
                    updateUserWhitelistFromRemote(remoteApps, context)

                    // 记录更新时间
                    recordRemoteWhitelistUpdateTime()

                    XLog.i("远程白名单更新完成，共更新 ${remoteApps.size} 个应用")
                } finally {
                    // 关闭HTTP客户端
                    httpClient.close()
                }

            } catch (e: Exception) {
                when (e) {
                    is kotlinx.serialization.SerializationException -> {
                        XLog.e("远程白名单JSON解析失败", e)
                    }
                    is java.net.UnknownHostException -> {
                        XLog.e("远程白名单服务器无法访问", e)
                    }
                    is java.net.ConnectException -> {
                        XLog.e("远程白名单服务器连接失败", e)
                    }
                    is java.net.SocketTimeoutException -> {
                        XLog.e("远程白名单请求超时", e)
                    }
                    else -> {
                        XLog.e("拉取远程白名单失败", e)
                    }
                }
            }
        }
    }

    /**
     * 检查是否应该更新远程白名单
     * @return true 如果距离上次更新超过10分钟
     */
    private fun shouldUpdateRemoteWhitelist(): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeDiff = currentTime - lastRemoteWhitelistUpdateTime

        return timeDiff >= NetworkConfig.REMOTE_WHITELIST_UPDATE_INTERVAL_MS
    }

    /**
     * 记录远程白名单更新时间（内存存储）
     */
    private fun recordRemoteWhitelistUpdateTime() {
        lastRemoteWhitelistUpdateTime = System.currentTimeMillis()
    }

    /**
     * 将远程白名单数据更新到本地用户白名单
     * @param remoteApps 远程应用列表
     * @param context 上下文，用于更新LockTask白名单
     */
    private fun updateUserWhitelistFromRemote(remoteApps: RemoteWhitelistResponse, context: Context?) {
        try {
            // 获取当前用户白名单
            val currentUserWhitelist = getUserWhitelistPackages().toMutableSet()

            // 提取远程应用的包名
            val remotePackageNames = remoteApps.map { it.packageName }.toSet()

            // 添加远程包名到用户白名单
            val originalSize = currentUserWhitelist.size
            currentUserWhitelist.addAll(remotePackageNames)
            val newSize = currentUserWhitelist.size

            // 保存更新后的白名单
            saveUserWhitelist(currentUserWhitelist)

            // 同步更新LockTask白名单
            updateLockTaskPackages(context)

            val addedCount = newSize - originalSize
            XLog.d("远程白名单更新完成：原有 $originalSize 个，新增 $addedCount 个，总计 $newSize 个")

            // 记录详细的应用信息
            if (addedCount > 0) {
                val originalUserWhitelist = getUserWhitelistPackages()
                val newApps = remoteApps.filter { !originalUserWhitelist.contains(it.packageName) }
                newApps.forEach { app ->
                    XLog.d("新增白名单应用：${app.appName} (${app.packageName})")
                }
            }

        } catch (e: Exception) {
            XLog.e("更新用户白名单失败", e)
        }
    }

}

/**
 * 应用信息数据类
 */
data class AppInfo(
    val packageName: String,
    val appName: String,
    val isInWhitelist: Boolean
)