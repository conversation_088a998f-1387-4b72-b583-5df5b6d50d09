package com.hwb.timecontroller.business

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import com.hjq.toast.Toaster
import com.hwb.timecontroller.BuildConfig
import com.hwb.timecontroller.network.NetworkManager
import com.hwb.timecontroller.network.UpdateCheckResponse
import com.hwb.timecontroller.network.UpdateInfo
import com.hwb.timecontroller.utils.UpdateUtils
import com.xuexiang.xupdate.XUpdate
import com.xuexiang.xupdate.entity.UpdateEntity
import com.xuexiang.xupdate.listener.IUpdateParseCallback
import com.xuexiang.xupdate.proxy.IUpdateParser
import com.xuexiang.xupdate.service.OnFileDownloadListener
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import com.elvishew.xlog.XLog

/**
 * 应用更新管理器
 * 负责检查应用更新、下载和安装
 */
object UpdateManager {

    private const val UPDATE_CHECK_URL = "https://game.3ddu.cn/estate/v1/checkUpdate"

    /**
     * 检查应用更新
     * @param context 上下文
     * @param showNoUpdateToast 是否在没有更新时显示提示
     */
    fun checkUpdate(context: Context, showNoUpdateToast: Boolean = false) {
        try {
            // 检查网络状态
            if (!isNetworkAvailable(context)) {
                XLog.w("网络不可用，无法检查更新")
                return
            }

            XUpdate.newBuild(context)
                .updateUrl(UPDATE_CHECK_URL)
                .updateParser(CustomUpdateParser(context))
                .themeColor(context.getColor(android.R.color.holo_blue_bright))
                .supportBackgroundUpdate(true)
                .promptWidthRatio(0.8f)  // 设置弹窗宽度比例
                .promptHeightRatio(-1f)  // 高度自适应
                .update()
        } catch (e: Exception) {
            XLog.e("启动更新检查失败", e)
        }
    }

    /**
     * 静默检查更新（不显示UI）
     * @param context 上下文
     * @param callback 检查结果回调
     */
    fun checkUpdateSilently(
        context: Context,
        callback: (hasUpdate: Boolean, updateInfo: UpdateInfo?) -> Unit
    ) {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val response = NetworkManager.httpClient.get(UPDATE_CHECK_URL)

                if (response.status == HttpStatusCode.OK) {
                    val updateResponse = response.body<UpdateCheckResponse>()

                    withContext(Dispatchers.Main) {
                        if (updateResponse.code == 200 && updateResponse.rspdata != null) {
                            val updateInfo = updateResponse.rspdata
                            val currentVersionName = UpdateUtils.getVersionName(context)
                            val hasUpdate =
                                isVersionNewer(updateInfo.latestVersion, currentVersionName)

                            callback(hasUpdate, if (hasUpdate) updateInfo else null)
                        } else {
                            callback(false, null)
                        }
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        callback(false, null)
                    }
                }
            } catch (e: Exception) {
                XLog.e("静默检查更新失败", e)
                withContext(Dispatchers.Main) {
                    callback(false, null)
                }
            }
        }
    }

    /**
     * 直接下载APK文件
     * @param context 上下文
     * @param downloadUrl 下载地址
     * @param listener 下载监听器
     */
    fun downloadApk(
        context: Context,
        downloadUrl: String,
        listener: OnFileDownloadListener
    ) {
        try {
            XUpdate.newBuild(context)
                .build()
                .download(downloadUrl, listener)
        } catch (e: Exception) {
            XLog.e("启动APK下载失败", e)
            listener.onError(e)
        }
    }

    /**
     * 检查强制更新
     * @param context 上下文
     * @param callback 检查结果回调
     */
    fun checkForceUpdate(context: Context, callback: (isForceUpdate: Boolean) -> Unit) {
        checkUpdateSilently(context) { hasUpdate, updateInfo ->
            val isForceUpdate = hasUpdate && updateInfo?.forceUpdate == true
            callback(isForceUpdate)
        }
    }

    /**
     * 比较版本号是否更新
     * @param newVersion 新版本号
     * @param currentVersion 当前版本号
     * @return true表示新版本更新
     */
    private fun isVersionNewer(newVersion: String, currentVersion: String): Boolean {
        return try {
            val newParts = newVersion.split(".").map { it.toIntOrNull() ?: 0 }
            val currentParts = currentVersion.split(".").map { it.toIntOrNull() ?: 0 }

            val maxLength = maxOf(newParts.size, currentParts.size)

            for (i in 0 until maxLength) {
                val newPart = newParts.getOrNull(i) ?: 0
                val currentPart = currentParts.getOrNull(i) ?: 0

                when {
                    newPart > currentPart -> return true
                    newPart < currentPart -> return false
                }
            }
            false
        } catch (e: Exception) {
            XLog.e("版本号比较失败: new=$newVersion, current=$currentVersion", e)
            false
        }
    }

    /**
     * 自定义更新解析器
     * 将服务器返回的数据转换为XUpdate需要的格式
     */
    private class CustomUpdateParser(private val context: Context) : IUpdateParser {
        override fun parseJson(json: String?): UpdateEntity? {
            if (json == null) {
                return null
            }
            if (BuildConfig.DEBUG){
                Toaster.showShort("测试，跳过更新")
                return null
            }
            try {
                XLog.d("开始解析更新数据: $json")

                val response = Json {
                    ignoreUnknownKeys = true
                    isLenient = true
                }.decodeFromString<UpdateCheckResponse>(json)

                if (response.code == 200 && response.rspdata != null) {
                    val updateInfo = response.rspdata
                    val currentVersionName = UpdateUtils.getVersionName(context)

                    // 检查是否有新版本
                    val hasUpdate = isVersionNewer(updateInfo.latestVersion, currentVersionName)

                    if (hasUpdate) {
                        XLog.d("发现新版本: ${updateInfo.latestVersion}, 当前版本: $currentVersionName")

                        val updateEntity = UpdateEntity()
                            .setHasUpdate(true)
                            .setForce(updateInfo.forceUpdate)
                            .setVersionName(updateInfo.latestVersion)
                            .setUpdateContent(updateInfo.updateContent)
                            .setDownloadUrl(updateInfo.downloadUrl)
                            .setSize(0) // 服务器没有返回文件大小，设为0

                        return updateEntity
                    } else {
                        XLog.d("当前已是最新版本: $currentVersionName")
                        val updateEntity = UpdateEntity().setHasUpdate(false)
                        return updateEntity
                    }
                } else {
                    XLog.w("msg=${response.msg}，服务器响应异常: code=${response.code}")
                    val updateEntity = UpdateEntity().setHasUpdate(false)
                    return updateEntity
                }
            } catch (e: Exception) {
                XLog.e("解析更新数据失败: $json", e)
            }
            return null
        }

        override fun parseJson(json: String, callback: IUpdateParseCallback) {
            parseJson(json)?.let {
                callback.onParseResult(it)
            }

        }

        override fun isAsyncParser(): Boolean {
            return false // 同步解析
        }

    }

    /**
     * 检查网络是否可用
     */
    private fun isNetworkAvailable(context: Context): Boolean {
        return try {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities =
                connectivityManager.getNetworkCapabilities(network) ?: return false

            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
        } catch (e: Exception) {
            XLog.e("检查网络状态失败", e)
            false
        }
    }

    /**
     * 检查是否为WiFi网络
     */
    private fun isWifiNetwork(context: Context): Boolean {
        return try {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork ?: return false
            val networkCapabilities =
                connectivityManager.getNetworkCapabilities(network) ?: return false

            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
        } catch (e: Exception) {
            XLog.e("检查WiFi状态失败", e)
            false
        }
    }
}
