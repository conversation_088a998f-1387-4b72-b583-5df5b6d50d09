package com.hwb.timecontroller.model

/**
 * 应用数据清理进度项数据模型
 * <p>
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/7/8: First created this class.
 */
data class CleanupProgressItem(
    val packageName: String,
    val appName: String,
    val status: CleanupStatus,
    val progress: Int = 0 // 0-100
) {
    
    /**
     * 清理状态枚举
     */
    enum class CleanupStatus {
        WAITING,    // 等待清理
        CLEANING,   // 正在清理
        SUCCESS,    // 清理成功
        FAILED      // 清理失败
    }
    
    /**
     * 获取状态显示文本
     */
    fun getStatusText(): String {
        return when (status) {
            CleanupStatus.WAITING -> "等待清理"
            CleanupStatus.CLEANING -> "正在清理..."
            CleanupStatus.SUCCESS -> "清理完成"
            CleanupStatus.FAILED -> "清理失败"
        }
    }
    
    /**
     * 获取状态颜色资源ID
     */
    fun getStatusColorRes(): Int {
        return when (status) {
            CleanupStatus.WAITING -> android.R.color.darker_gray
            CleanupStatus.CLEANING -> android.R.color.holo_blue_light
            CleanupStatus.SUCCESS -> android.R.color.holo_green_light
            CleanupStatus.FAILED -> android.R.color.holo_red_light
        }
    }
}
