package com.hwb.timecontroller.model

import kotlinx.serialization.Serializable

/**
 * 用户信息数据类
 * 
 * Author: huangwubin
 * Date: 2025/7/4
 */
@Serializable
data class UserInfo(
    /**
     * 用户ID（服务器端用户标识）
     */
    val userId: String? = null,

    /**
     * 用户名
     */
    val userName: String? = null,

    /**
     * 微信OpenID
     */
    val openId: String? = null,

    /**
     * 用户余额
     */
    val money: Double? = null,

    /**
     * 剩余时长（秒）
     */
    val temp_time: Int? = null,

    /**
     * 用户头像URL
     */
    val headImg: String? = null,

    /**
     * 账户创建时间（服务器返回的字符串格式）
     */
    val createTime: String? = null,

    /**
     * 登录时间戳（毫秒）
     */
    val loginTime: Long? = null,

    /**
     * 最后活跃时间戳（毫秒）
     */
    val lastActiveTime: Long? = null,

    /**
     * 设备UUID（UUIDv7格式）
     */
    val deviceId: String? = null,

    /**
     * 用户登录状态
     */
    val isLoggedIn: Boolean = false,

    /**
     * 扩展信息（JSON格式，用于存储额外的用户数据）
     */
    val extraInfo: String? = null
) {
    
    /**
     * 检查用户是否已登录且登录未过期
     * @param expirationTimeMs 登录过期时间（毫秒），默认24小时
     * @return 是否为有效登录状态
     */
    fun isValidLogin(expirationTimeMs: Long = 24 * 60 * 60 * 1000L): Boolean {
        if (!isLoggedIn || loginTime == null) {
            return false
        }
        
        val currentTime = System.currentTimeMillis()
        return (currentTime - loginTime) < expirationTimeMs
    }
    
    /**
     * 获取登录持续时间（毫秒）
     * @return 登录持续时间，如果未登录返回0
     */
    fun getLoginDuration(): Long {
        return if (isLoggedIn && loginTime != null) {
            System.currentTimeMillis() - loginTime
        } else {
            0L
        }
    }
    
    /**
     * 获取最后活跃时间距离现在的时长（毫秒）
     * @return 距离最后活跃的时长，如果没有记录返回Long.MAX_VALUE
     */
    fun getTimeSinceLastActive(): Long {
        return if (lastActiveTime != null) {
            System.currentTimeMillis() - lastActiveTime
        } else {
            Long.MAX_VALUE
        }
    }
    
    /**
     * 创建更新最后活跃时间的副本
     * @return 更新了lastActiveTime的UserInfo副本
     */
    fun updateLastActiveTime(): UserInfo {
        return copy(lastActiveTime = System.currentTimeMillis())
    }
    
    /**
     * 创建登录状态的副本
     * @param loggedIn 登录状态
     * @return 更新了登录状态和登录时间的UserInfo副本
     */
    fun updateLoginStatus(loggedIn: Boolean): UserInfo {
        return if (loggedIn) {
            copy(
                isLoggedIn = true,
                loginTime = System.currentTimeMillis(),
                lastActiveTime = System.currentTimeMillis()
            )
        } else {
            copy(
                isLoggedIn = false,
                loginTime = null
            )
        }
    }
    
    companion object {
        /**
         * 创建默认的用户信息
         * @param deviceId 设备ID
         * @return 默认UserInfo实例
         */
        fun createDefault(deviceId: String): UserInfo {
            return UserInfo(
                deviceId = deviceId,
                isLoggedIn = false,
                lastActiveTime = System.currentTimeMillis()
            )
        }
        
        /**
         * 创建已登录的用户信息
         * @param deviceId 设备ID
         * @param userId 用户ID（可选）
         * @param userName 用户名（可选）
         * @param openId 微信OpenID（可选）
         * @param money 用户余额（可选）
         * @param temp_time 剩余时长（可选）
         * @param headImg 用户头像URL（可选）
         * @param createTime 账户创建时间（可选）
         * @return 已登录的UserInfo实例
         */
        fun createLoggedIn(
            deviceId: String,
            userId: String? = null,
            userName: String? = null,
            openId: String? = null,
            money: Double? = null,
            temp_time: Int? = null,
            headImg: String? = null,
            createTime: String? = null
        ): UserInfo {
            val currentTime = System.currentTimeMillis()
            return UserInfo(
                userId = userId,
                userName = userName,
                openId = openId,
                money = money,
                temp_time = temp_time,
                headImg = headImg,
                createTime = createTime,
                deviceId = deviceId,
                isLoggedIn = true,
                loginTime = currentTime,
                lastActiveTime = currentTime
            )
        }

        /**
         * 从ClientLoginData创建UserInfo
         * @param clientData 客户端登录数据
         * @param deviceId 设备ID
         * @return UserInfo实例
         */
        fun fromClientLoginData(
            clientData: com.hwb.timecontroller.network.ClientLoginData,
            deviceId: String
        ): UserInfo {
            val currentTime = System.currentTimeMillis()
            return UserInfo(
                userId = clientData.id,
                userName = clientData.userName,
                openId = clientData.openId,
                money = clientData.money,
                temp_time = clientData.temp_time,
                headImg = clientData.headImg,
                createTime = clientData.createTime,
                deviceId = deviceId,
                isLoggedIn = true,
                loginTime = currentTime,
                lastActiveTime = currentTime
            )
        }
    }
}

/**
 * 用户登录状态枚举
 */
enum class UserLoginStatus {
    /**
     * 未登录
     */
    NOT_LOGGED_IN,
    
    /**
     * 已登录
     */
    LOGGED_IN,
    
    /**
     * 登录已过期
     */
    LOGIN_EXPIRED,
    
    /**
     * 登录状态未知（初始化中或出错）
     */
    UNKNOWN
}
