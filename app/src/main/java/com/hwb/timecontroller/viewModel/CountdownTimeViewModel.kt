package com.hwb.timecontroller.viewModel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hwb.timecontroller.business.CountdownManager
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import java.util.concurrent.TimeUnit

/**
 * 倒计时ViewModel，全局唯一
 * 使用CountdownManager的StateFlow来管理倒计时状态
 * <p>
 * Author:huangwubin
 * <p>
 * changeLogs:
 * 2025/6/30: First created this class.
 */
class CountdownTimeViewModel : ViewModel() {

    /**
     * 倒计时数据StateFlow
     * 从CountdownManager获取，并在ViewModel作用域内共享
     */
    val countdownData: StateFlow<CountdownManager.CountdownData> =
        CountdownManager.countdownData.stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = CountdownManager.CountdownData()
        )

    /**
     * 获取格式化的剩余时间字符串
     * @return 格式化的时间字符串 (MM:SS)
     */
    fun getFormattedRemainingTime(): String {
        val remainingMillis = countdownData.value.remainingTimeMillis
        val minutes = TimeUnit.MILLISECONDS.toMinutes(remainingMillis)
        val seconds = TimeUnit.MILLISECONDS.toSeconds(remainingMillis) % 60
        return String.format("%02d:%02d", minutes, seconds)
    }

    /**
     * 获取倒计时进度百分比
     * @return 进度百分比 (0.0 - 1.0)
     */
    fun getProgress(): Float {
        val data = countdownData.value
        return if (data.totalDurationMillis > 0) {
            1.0f - (data.remainingTimeMillis.toFloat() / data.totalDurationMillis.toFloat())
        } else {
            0.0f
        }
    }

    /**
     * 检查倒计时是否正在运行（委托给CountdownManager）
     */
    fun isCountdownRunning(): Boolean {
        return CountdownManager.isCountdownRunning()
    }

    /**
     * 检查设备是否锁定
     */
    fun isDeviceLocked(): Boolean {
        return countdownData.value.isDeviceLocked
    }

    /**
     * 检查倒计时是否已结束（委托给CountdownManager）
     */
    fun isCountdownFinished(): Boolean {
        return CountdownManager.isCountdownFinished()
    }
}