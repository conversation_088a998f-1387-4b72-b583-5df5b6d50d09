package com.hwb.timecontroller.viewModel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.hwb.timecontroller.network.*
import com.hwb.timecontroller.business.UserManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.Job
import com.elvishew.xlog.XLog
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.qrcode.QRCodeWriter
import android.graphics.Bitmap
import android.graphics.Color
import android.util.Base64
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import java.io.ByteArrayOutputStream

/**
 * 登录验证ViewModel
 * 处理登录验证流程的业务逻辑和状态管理
 */
class LoginViewModel(application: Application) : AndroidViewModel(application) {

    // 登录状态
    private val _loginState = MutableLiveData<LoginState>()

    // 登录二维码Base64数据
    private val _qrCodeBase64 = MutableLiveData<String>()
    val qrCodeBase64: LiveData<String> = _qrCodeBase64

    // 状态提示文本
    private val _statusText = MutableLiveData<String>()

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()

    // HTTP客户端
    private val httpClient = NetworkManager.httpClient

    // 1分钟倒计时相关
    private val _countdownSeconds = MutableLiveData<Int>()
    val countdownSeconds: LiveData<Int> = _countdownSeconds

    private val _countdownFinished = MutableLiveData<Boolean>()
    val countdownFinished: LiveData<Boolean> = _countdownFinished

    private var countdownJob: Job? = null

    // HTTP轮询相关
    private var pollingJob: Job? = null
    private val _pollingActive = MutableLiveData<Boolean>()
    val pollingActive: LiveData<Boolean> = _pollingActive

    // 内存中的用户信息（不持久化）
    private val _currentUserData = MutableLiveData<ClientLoginData?>()

    // 跳转到用户信息页面的事件
    private val _navigateToUserInfo = MutableLiveData<ClientLoginData?>()
    val navigateToUserInfo: LiveData<ClientLoginData?> = _navigateToUserInfo

    init {
        _loginState.value = LoginState.IDLE
        _statusText.value = "准备获取登录二维码..."
        _pollingActive.value = false

        // 监听全局WebSocket消息 - 暂时注释，后续改为HTTP长轮询
        // observeWebSocketMessages()

        // 监听WebSocket连接状态 - 暂时注释，后续改为HTTP长轮询
        // observeWebSocketConnectionState()
    }

    /**
     * 直接生成二维码（不需要应用信息）
     */
    fun generateQRCodeDirectly() {
        XLog.d("直接生成二维码")

        _loginState.value = LoginState.LOADING_QR
        _statusText.value = "正在生成登录二维码..."

        viewModelScope.launch {
            try {
                // 生成本地二维码
                val secretKey = getSecretKey()
                val qrContent = "http://book.3ddu.cn?loginCode=$secretKey"
                val qrCodeBase64 = generateQRCodeBase64(qrContent)

                if (qrCodeBase64 != null) {
                    _qrCodeBase64.value = qrCodeBase64
                    _loginState.value = LoginState.WAITING_LOGIN
                    _statusText.value = "请扫描二维码完成登录验证"
                    XLog.d("登录二维码生成成功，内容: $qrContent")
                } else {
                    _loginState.value = LoginState.ERROR
                    _statusText.value = "生成登录二维码失败"
                    _errorMessage.value = "二维码生成错误"
                    XLog.e("生成登录二维码失败")
                }

            } catch (e: Exception) {
                _loginState.value = LoginState.ERROR
                _statusText.value = "生成二维码失败"
                _errorMessage.value = e.message ?: "未知错误"
                XLog.e("生成登录二维码时发生异常", e)
            }
        }
    }

    /**
     * 取消登录
     */
    fun cancelLogin() {
        XLog.d("用户取消登录")
        _loginState.value = LoginState.CANCELLED
        _statusText.value = "已取消登录"
    }

    /**
     * 手动刷新登录状态（主动查询一次）
     */
    fun refreshLoginStatus() {
        XLog.d("用户手动刷新登录状态")

        viewModelScope.launch {
            try {
                // 调用clientLogin接口进行一次查询 - 直接拼接URL避免自动编码
                val secretKey = getSecretKey()
                val fullUrl = "${NetworkConfig.BASE_URL}${NetworkConfig.BASE_PATH}/${NetworkConfig.ENDPOINT_CLIENT_LOGIN}?secretKey=$secretKey"
                val response = httpClient.get(fullUrl)

                if (response.status == HttpStatusCode.OK) {
                    val clientLoginResponse = response.body<ClientLoginResponse>()
                    handleClientLoginResponse(clientLoginResponse)
                    XLog.d("手动刷新登录状态成功")
                } else {
                    XLog.w("手动刷新登录状态失败: ${response.status}")
                    _errorMessage.value = "刷新失败，请稍后重试"
                }

            } catch (e: Exception) {
                XLog.e("手动刷新登录状态异常", e)
                _errorMessage.value = "网络异常，请检查网络连接"
            }
        }
    }

    /**
     * 获取设备ID
     */
    private fun getDeviceId(): String {
        return try {
            UserManager.getDeviceId()
        } catch (e: Exception) {
            XLog.e("获取设备ID失败", e)
            "unknown_device"
        }
    }

    /**
     * 获取secretKey（设备ID + "_" + 6位随机码）
     */
    private fun getSecretKey(): String {
        return try {
            UserManager.getSecretKey()
        } catch (e: Exception) {
            XLog.e("获取secretKey失败", e)
            "unknown_device_000000"
        }
    }

    /**
     * 生成二维码并转换为Base64字符串
     * @param content 二维码内容
     * @return Base64编码的二维码图片，失败返回null
     */
    private fun generateQRCodeBase64(content: String): String? {
        return try {
            val writer = QRCodeWriter()
            // 设置编码参数，移除边距
            val hints = hashMapOf<EncodeHintType, Any>()
            hints[EncodeHintType.MARGIN] = 0  // 移除边距

            val bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, 512, 512, hints)
            val width = bitMatrix.width
            val height = bitMatrix.height

            // 使用ARGB_8888格式支持透明背景
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)

            for (x in 0 until width) {
                for (y in 0 until height) {
                    // 黑色像素保持黑色，白色像素设为透明
                    bitmap.setPixel(x, y, if (bitMatrix[x, y]) Color.BLACK else Color.TRANSPARENT)
                }
            }

            // 将Bitmap转换为Base64
            val byteArrayOutputStream = ByteArrayOutputStream()
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
            val byteArray = byteArrayOutputStream.toByteArray()
            Base64.encodeToString(byteArray, Base64.DEFAULT)

        } catch (e: Exception) {
            XLog.e("生成二维码失败", e)
            null
        }
    }

    /**
     * 启动3分钟倒计时（倒计时结束入口专用）
     */
    fun startThreeMinuteCountdown() {
        XLog.d("启动3分钟倒计时")

        countdownJob?.cancel()

        countdownJob = viewModelScope.launch {
            try {
                for (seconds in 180 downTo 1) {
                    _countdownSeconds.value = seconds
                    delay(1000)
                }

                // 倒计时结束
                _countdownSeconds.value = 0
                _countdownFinished.value = true
                XLog.d("3分钟倒计时结束")

            } catch (e: Exception) {
                XLog.e("3分钟倒计时执行失败", e)
            }
        }
    }

    /**
     * 启动2分钟倒计时（其他入口专用）
     */
    fun startTwoMinuteCountdown() {
        XLog.d("启动2分钟倒计时")

        countdownJob?.cancel()

        countdownJob = viewModelScope.launch {
            try {
                for (seconds in 120 downTo 1) {
                    _countdownSeconds.value = seconds
                    delay(1000)
                }

                // 倒计时结束
                _countdownSeconds.value = 0
                _countdownFinished.value = true
                XLog.d("2分钟倒计时结束")

            } catch (e: Exception) {
                XLog.e("2分钟倒计时执行失败", e)
            }
        }
    }

    /**
     * 取消倒计时
     */
    fun cancelCountdown() {
        countdownJob?.cancel()
        _countdownSeconds.value = 0
        XLog.d("倒计时已取消")
    }

    /**
     * 暂停倒计时
     */
    fun pauseCountdown() {
        countdownJob?.cancel()
        XLog.d("倒计时已暂停")
    }

    /**
     * 恢复倒计时 - 重新开始
     */
    fun resumeCountdown(entryType: String) {
        XLog.d("恢复倒计时 - entryType: $entryType")
        when (entryType) {
            "COUNTDOWN_ENDED" -> startThreeMinuteCountdown()
            "THIRD_PARTY_APP" -> startTwoMinuteCountdown()
            else -> {
                XLog.w("未知的入口类型，启动默认2分钟倒计时")
                startTwoMinuteCountdown()
            }
        }
    }

    /**
     * 启动HTTP轮询机制
     */
    fun startHttpPolling() {
        if (_pollingActive.value == true) {
            XLog.d("HTTP轮询已在运行，忽略重复启动")
            return
        }

        XLog.d("启动HTTP轮询机制")
        _pollingActive.value = true

        pollingJob = viewModelScope.launch {
            try {
                while (_pollingActive.value == true) {
                    try {
                        // 调用clientLogin接口 - 直接拼接URL避免自动编码
                        val secretKey = getSecretKey()
                        val fullUrl = "${NetworkConfig.BASE_URL}${NetworkConfig.BASE_PATH}/${NetworkConfig.ENDPOINT_CLIENT_LOGIN}?secretKey=$secretKey"
                        val response = httpClient.get(fullUrl)
                        XLog.d("GET请求响应: ${response.bodyAsText()}")

                        if (response.status == HttpStatusCode.OK) {
                            val clientLoginResponse = response.body<ClientLoginResponse>()
                            handleClientLoginResponse(clientLoginResponse)
                        } else {
                            XLog.w("HTTP轮询请求失败: ${response.status}")
                        }

                    } catch (e: Exception) {
                        XLog.e("HTTP轮询请求异常，继续轮询", e)
                    }

                    // 等待10秒后继续下一次轮询
                    if (_pollingActive.value == true) {
                        delay(3000)
                    }
                }
            } catch (e: Exception) {
                XLog.e("HTTP轮询协程异常", e)
            } finally {
                _pollingActive.value = false
                XLog.d("HTTP轮询已停止")
            }
        }
    }

    /**
     * 停止HTTP轮询机制
     */
    fun stopHttpPolling() {
        XLog.d("停止HTTP轮询机制")
        _pollingActive.value = false
        pollingJob?.cancel()
        pollingJob = null
    }

    /**
     * 暂停HTTP轮询机制
     */
    fun pauseHttpPolling() {
        XLog.d("暂停HTTP轮询机制")
        _pollingActive.value = false
        pollingJob?.cancel()
    }

    /**
     * 恢复HTTP轮询机制
     */
    fun resumeHttpPolling() {
        XLog.d("恢复HTTP轮询机制")
        //延时500 毫秒
        viewModelScope.launch {
            delay(500)
            startHttpPolling()
        }
    }

    /**
     * 处理clientLogin接口响应
     */
    private fun handleClientLoginResponse(response: ClientLoginResponse) {
        try {
            XLog.d("收到clientLogin响应: code=${response.code}, msg=${response.msg}")
            XLog.d("rspdata内容: ${response.rspdata}")

            // 检查rspdata中是否包含id字段（实际的用户ID）
            val clientData = response.rspdata
            val userId = clientData?.id
            XLog.d("解析结果: userId=$userId, clientData=$clientData")
            if (!userId.isNullOrEmpty()) {
                XLog.d("检测到用户登录成功: userId=$userId, userName=${clientData.userName}")

                // 停止轮询
                stopHttpPolling()

                // 更新登录状态
                _loginState.value = LoginState.SUCCESS
                _statusText.value = "登录成功！"

                // 保存基本登录状态到持久化存储
                UserManager.setUserLoggedIn(true, userId, clientData.userName)
                UserManager.updateUserActiveTime()

                // 保存完整用户数据到内存中（不持久化）
                _currentUserData.value = clientData

                // 保存完整的客户端登录数据到UserManager
                UserManager.setClientLoginData(clientData)

                // 触发跳转到用户信息页面
                _navigateToUserInfo.value = clientData

            } else {
                XLog.d("用户尚未登录，继续轮询")
            }

        } catch (e: Exception) {
            XLog.e("处理clientLogin响应失败", e)
        }
    }

    /**
     * 清除跳转事件（避免重复跳转）
     */
    fun clearNavigateToUserInfo() {
        _navigateToUserInfo.value = null
    }

    /**
     * 清理资源
     */
    override fun onCleared() {
        super.onCleared()
        countdownJob?.cancel()
        stopHttpPolling()
        _currentUserData.value = null
        XLog.d("LoginViewModel已清理")
    }
}
