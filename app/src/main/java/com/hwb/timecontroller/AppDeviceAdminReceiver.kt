package com.hwb.timecontroller

import android.app.admin.DeviceAdminReceiver
import android.content.Context
import android.content.Intent
import com.hwb.timecontroller.business.AdminManager
import com.elvishew.xlog.XLog

/**
 * 设备管理接收器
 * 用于满足设备所有者要求的空实现
 */
class AppDeviceAdminReceiver : DeviceAdminReceiver() {
    companion object {
        /// 是否手动退出管控状态的
        var isManualExit: Boolean = false
    }

    // 空实现，用于满足设备所有者要求
    override fun onLockTaskModeEntering(context: Context, intent: Intent, pkg: String) {
        super.onLockTaskModeEntering(context, intent, pkg)
        //会打印：LockTask模式已进入: com.Liyichong.VR
        XLog.i("LockTask模式已进入: $pkg")
        isManualExit = false
    }

    override fun onLockTaskModeExiting(context: Context, intent: Intent) {
        super.onLockTaskModeExiting(context, intent)
        XLog.w("LockTask模式已退出")
        if (isManualExit) {
            isManualExit = false
        } else {
            //非常规手动退出管控状态，重启 app
            AdminManager.restartApp()
        }
    }
}
