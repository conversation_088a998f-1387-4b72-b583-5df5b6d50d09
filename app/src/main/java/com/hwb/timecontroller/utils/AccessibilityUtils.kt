package com.hwb.timecontroller.utils

import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.provider.Settings
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.elvishew.xlog.XLog
import com.hwb.timecontroller.AppDeviceAdminReceiver
import com.hwb.timecontroller.R
import com.hwb.timecontroller.service.AppLifecycleAccessibilityService

/**
 * 无障碍服务和设备所有者权限统一管理工具类
 * 合并了DeviceOwnerHelper和AccessibilityServiceManager的功能
 */
class AccessibilityUtils(private val context: Context) {

    companion object {
    }

    private val devicePolicyManager: DevicePolicyManager =
        context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    private val adminComponent = ComponentName(context, AppDeviceAdminReceiver::class.java)
    private var accessibilityDialog: AlertDialog? = null

    /**
     * 检查是否具有设备所有者权限
     */
    fun isDeviceOwner(): Boolean {
        return devicePolicyManager.isDeviceOwnerApp(context.packageName)
    }

    /**
     * 检查无障碍总开关是否已开启
     */
    fun isAccessibilityEnabled(): Boolean {
        return try {
            val accessibilityEnabled = Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
            accessibilityEnabled == 1
        } catch (e: Settings.SettingNotFoundException) {
            false
        }
    }

    /**
     * 检查无障碍服务是否已启用
     */
    fun isAccessibilityServiceEnabled(): Boolean {
        val accessibilityEnabled = try {
            Settings.Secure.getInt(
                context.contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
        } catch (e: Settings.SettingNotFoundException) {
            0
        }

        if (accessibilityEnabled == 1) {
            val services = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            )
            val serviceName =
                "${context.packageName}/${AppLifecycleAccessibilityService::class.java.name}"
            return services?.contains(serviceName) == true
        }

        return false
    }

    /**
     * 自动启用无障碍服务
     */
    fun autoEnableAccessibilityService(
        onStatusUpdate: (String) -> Unit,
        onServiceEnabled: () -> Unit,
        onServiceFailed: () -> Unit
    ) {
        if (!isDeviceOwner()) {
            onStatusUpdate("需要设备所有者权限才能自动启用无障碍服务")
            onServiceFailed()
            return
        }

        if (isAccessibilityServiceEnabled()) {
            onStatusUpdate(context.getString(R.string.accessibility_service_enabled))
            Toast.makeText(context, "无障碍服务已启用，应用监控功能正常", Toast.LENGTH_SHORT).show()
            onServiceEnabled()
            return
        }

        // 先检查无障碍总开关是否已开启
        if (isAccessibilityEnabled()) {
            // 总开关已开启，直接利用设备所有者权限添加我们的服务
            Toast.makeText(
                context,
                "检测到无障碍总开关已开启，正在自动添加服务...",
                Toast.LENGTH_SHORT
            ).show()
            enableAccessibilityServiceInternal(onStatusUpdate, onServiceEnabled, onServiceFailed)
        } else {
            // 总开关未开启，尝试引导用户开启
            showAccessibilityEnableDialog(onStatusUpdate)
        }
    }

    /**
     * 使用设备所有者权限启用无障碍服务
     */
    private fun enableAccessibilityServiceInternal(
        onStatusUpdate: (String) -> Unit,
        onServiceEnabled: () -> Unit,
        onServiceFailed: () -> Unit
    ): Boolean {
        if (!isDeviceOwner()) {
            XLog.w("当前不是 Device Owner，无法静默启用无障碍")
            onServiceFailed()
            return false
        }

        val serviceName =
            "${context.packageName}/${AppLifecycleAccessibilityService::class.java.name}"
        XLog.i("准备启用无障碍服务: $serviceName")

        try {
            // 检查无障碍总开关是否已开启
            val accessibilityEnabled = try {
                Settings.Secure.getInt(
                    context.contentResolver,
                    Settings.Secure.ACCESSIBILITY_ENABLED
                )
            } catch (e: Settings.SettingNotFoundException) {
                0
            }

            if (accessibilityEnabled != 1) {
                XLog.w("无障碍总开关未开启，无法添加服务到列表")
                onStatusUpdate("无障碍总开关未开启")
                onServiceFailed()
                return false
            }

            XLog.d("无障碍总开关已开启，准备添加服务到列表")

            // 添加到已启用的无障碍服务列表
            val oldEnabled = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
            ) ?: ""

            if (oldEnabled.contains(serviceName)) {
                XLog.d("服务已在启用列表中")
                onStatusUpdate(context.getString(R.string.accessibility_service_enabled))
                onServiceEnabled()
                return true
            }

            val newEnabled = if (oldEnabled.isEmpty()) {
                serviceName
            } else {
                "$oldEnabled:$serviceName"
            }

            devicePolicyManager.setSecureSetting(
                adminComponent,
                Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES,
                newEnabled
            )

            // 验证结果
            Thread.sleep(500) // 等待系统处理
            val success = isAccessibilityServiceEnabled()
            XLog.i("无障碍服务启用 ${if (success) "成功" else "失败"}")

            if (success) {
                onStatusUpdate(context.getString(R.string.accessibility_service_enabled))
                Toast.makeText(context, "无障碍服务已自动启用", Toast.LENGTH_SHORT).show()
                onServiceEnabled()
            } else {
                onStatusUpdate("无障碍服务启用失败")
                Toast.makeText(context, "无障碍服务启用失败，请检查权限", Toast.LENGTH_LONG).show()
                onServiceFailed()
            }

            return success
        } catch (e: Exception) {
            XLog.e("启用无障碍服务异常", e)
            onStatusUpdate("无障碍服务启用异常")
            onServiceFailed()
            return false
        }
    }

    /**
     * 显示无障碍开启引导对话框
     */
    private fun showAccessibilityEnableDialog(onStatusUpdate: (String) -> Unit) {
        if (accessibilityDialog?.isShowing == true) {
            return
        }

        if (context !is AppCompatActivity) {
            XLog.e("cannot show dialog", "Context is not an Activity")
            return
        }

        accessibilityDialog = AlertDialog.Builder(context)
            .setTitle(context.getString(R.string.accessibility_guide_title))
            .setMessage(context.getString(R.string.accessibility_guide_message))
            .setPositiveButton(context.getString(R.string.go_to_settings)) { _, _ ->
                openAccessibilitySettings()
            }
            .setNegativeButton(context.getString(R.string.later)) { dialog, _ ->
                dialog.dismiss()
                onStatusUpdate("无障碍服务未启用")
            }
            .setCancelable(false)
            .create()

        accessibilityDialog?.show()
    }

    /**
     * 打开无障碍设置页面
     */
    fun openAccessibilitySettings() {
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            context.startActivity(intent)
            Toast.makeText(context, "请找到并开启 Time Controller 无障碍服务", Toast.LENGTH_LONG)
                .show()
        } catch (e: Exception) {
            Toast.makeText(context, "无法打开无障碍设置", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 检查并自动添加我们的服务到无障碍列表
     */
    fun checkAndAutoAddService(
        onStatusUpdate: (String) -> Unit,
        onServiceEnabled: () -> Unit
    ) {
        // 如果对话框还在显示，说明用户可能刚从设置页面回来
        if (accessibilityDialog?.isShowing == true) {
            // 先检查我们的服务是否已经被用户手动启用了
            if (isAccessibilityServiceEnabled()) {
                // 服务已启用，关闭对话框
                accessibilityDialog?.dismiss()
                onStatusUpdate(context.getString(R.string.accessibility_service_enabled))
                Toast.makeText(context, "无障碍服务已启用", Toast.LENGTH_SHORT).show()
                onServiceEnabled()
                return
            }

            // 检查总开关是否已开启
            if (isAccessibilityEnabled()) {
                // 总开关已开启，关闭对话框并自动添加服务
                accessibilityDialog?.dismiss()
                Toast.makeText(
                    context,
                    "检测到无障碍总开关已开启，正在自动添加服务...",
                    Toast.LENGTH_SHORT
                ).show()
                enableAccessibilityServiceInternal(onStatusUpdate, onServiceEnabled) {}
            }
        }
    }

    /**
     * 更新无障碍服务状态显示
     */
    fun updateAccessibilityStatus(): String {
        val isEnabled = isAccessibilityServiceEnabled()
        return if (isEnabled) {
            context.getString(R.string.accessibility_service_enabled)
        } else {
            context.getString(R.string.accessibility_service_disabled)
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        accessibilityDialog?.dismiss()
        accessibilityDialog = null
    }
}
