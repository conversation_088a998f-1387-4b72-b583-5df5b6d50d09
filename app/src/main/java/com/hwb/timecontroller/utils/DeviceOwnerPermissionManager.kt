package com.hwb.timecontroller.utils

import android.app.AppOpsManager
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.provider.Settings
import com.hwb.timecontroller.AppDeviceAdminReceiver
import com.elvishew.xlog.XLog

/**
 * 设备所有者权限管理工具类
 * 使用设备所有者权限自动授予各种系统权限
 */
class DeviceOwnerPermissionManager(private val context: Context) {

    companion object {
    }

    private val devicePolicyManager: DevicePolicyManager =
        context.getSystemService(Context.DEVICE_POLICY_SERVICE) as DevicePolicyManager
    private val adminComponent = ComponentName(context, AppDeviceAdminReceiver::class.java)

    /**
     * 检查是否具有设备所有者权限
     */
    fun isDeviceOwner(): Boolean {
        return try {
            devicePolicyManager.isDeviceOwnerApp(context.packageName)
        } catch (e: Exception) {
            XLog.e("检查设备所有者权限失败", e)
            false
        }
    }

    /**
     * 自动授予悬浮窗权限
     * 使用设备所有者权限直接设置系统权限
     */
    fun grantOverlayPermission(): Boolean {
        // 1. 确认我们是设备所有者
        if (!devicePolicyManager.isDeviceOwnerApp(context.packageName)) {
            XLog.w("非设备所有者，无法自动授予悬浮窗权限。")
            return false
        }
        // 2. 检查是否已经拥有权限
        if (Settings.canDrawOverlays(context)) {
            XLog.d("悬浮窗权限已存在，无需重复授予。")
            return true
        }

        // 3. 使用反射调用隐藏的 setMode 方法
        XLog.i("作为设备所有者，正在通过「反射」调用隐藏API AppOpsManager.setMode...")
        try {
            val appOpsManager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            val packageName = context.packageName
            val uid = context.applicationInfo.uid
            // 反射的核心：通过方法名和参数类型找到那个隐藏的方法
            val setModeMethod = AppOpsManager::class.java.getMethod(
                "setMode", // 方法名
                Int::class.java,    // 第1个参数类型：int (op)
                Int::class.java,    // 第2个参数类型：int (uid)
                String::class.java, // 第3个参数类型：String (packageName)
                Int::class.java     // 第4个参数类型：int (mode)
            )
            // 成功找到方法后，调用它
            // 注意：这里用的是 OP_SYSTEM_ALERT_WINDOW (整数)，而不是 OPSTR_ (字符串)
            setModeMethod.invoke(
                appOpsManager, // 在哪个对象上调用
                AppOpsManager.OPSTR_SYSTEM_ALERT_WINDOW, // 参数1
                uid,                                  // 参数2
                packageName,                          // 参数3
                AppOpsManager.MODE_ALLOWED            // 参数4
            )
            // 短暂等待，让系统设置生效
            Thread.sleep(200)
            // 4. 最终验证
            if (Settings.canDrawOverlays(context)) {
                XLog.d("成功！已通过「反射」授予悬浮窗权限。")
                return true
            } else {
                XLog.e("失败！反射调用成功后，权限检查依然为 false。")
                return false
            }
        } catch (e: Exception) {
            XLog.e("通过「反射」授予悬浮窗权限时发生异常", e)
            return false
        }
    }
//    fun grantOverlayPermission(): Boolean {
//        if (!isDeviceOwner()) {
//            XLog.w( "没有设备所有者权限，无法自动授予悬浮窗权限")
//            return false
//        }
//
//        return try {
//            // 检查是否已经有权限
//            if (Settings.canDrawOverlays(context)) {
//                XLog.d( "悬浮窗权限已存在")
//                return true
//            }
//
//            // 方法1: 设置权限策略为自动授予 (优先尝试)
//            try {
//                devicePolicyManager.setPermissionPolicy(
//                    adminComponent,
//                    DevicePolicyManager.PERMISSION_POLICY_AUTO_GRANT
//                )
//                XLog.d( "设置权限策略为自动授予")
//
//                // 等待一下让系统处理
//                Thread.sleep(500)
//
//                // 检查是否生效
//                if (Settings.canDrawOverlays(context)) {
//                    XLog.d( "权限策略设置成功，悬浮窗权限已获得")
//                    return true
//                }
//            } catch (e: Exception) {
//                XLog.w(e,  "设置权限策略失败")
//            }
//
//            // 方法2: 使用setPermissionGrantState直接授予权限
//            try {
//                val result = devicePolicyManager.setPermissionGrantState(
//                    adminComponent,
//                    context.packageName,
//                    android.Manifest.permission.SYSTEM_ALERT_WINDOW,
//                    DevicePolicyManager.PERMISSION_GRANT_STATE_GRANTED
//                )
//                XLog.d( "setPermissionGrantState结果: $result")
//
//                // 等待一下让系统处理
//                Thread.sleep(500)
//
//                // 检查是否成功
//                if (Settings.canDrawOverlays(context)) {
//                    XLog.d( "使用setPermissionGrantState成功授予悬浮窗权限")
//                    return true
//                }
//            } catch (e: Exception) {
//                XLog.w(e,  "setPermissionGrantState失败")
//            }
//
//            // 尝试直接修改系统设置（需要WRITE_SECURE_SETTINGS权限）
//            try {
//                // 这个方法在某些设备上可能有效
//                val packageName = context.packageName
//                val currentValue = Settings.Secure.getString(
//                    context.contentResolver,
//                    "enabled_accessibility_services"
//                ) ?: ""
//
//                // 注意：这里我们不是设置无障碍服务，而是尝试其他系统设置
//                XLog.d( "尝试通过系统设置授予权限")
//
//                // 对于悬浮窗权限，我们可能需要使用不同的方法
//                // 在某些设备上，设备所有者可以直接修改应用的权限状态
//
//            } catch (e: Exception) {
//                XLog.w(e,  "直接修改系统设置失败")
//            }
//
//            // 最后检查权限是否已经被授予
//            val hasPermission = Settings.canDrawOverlays(context)
//            XLog.d( "悬浮窗权限授予${if (hasPermission) "成功" else "失败"}")
//            return hasPermission
//
//        } catch (e: Exception) {
//            Log.e(TAG, "自动授予悬浮窗权限失败", e)
//            false
//        }
//    }

    /**
     * 检查悬浮窗权限状态
     */
    fun hasOverlayPermission(): Boolean {
        return Settings.canDrawOverlays(context)
    }

    /**
     * 尝试使用设备所有者权限自动处理悬浮窗权限
     * 如果自动授予失败，返回false表示需要手动处理
     */
    fun ensureOverlayPermission(): Boolean {
        if (!isDeviceOwner()) {
            XLog.w("没有设备所有者权限")
            return false
        }

        // 检查是否已经有权限
        if (hasOverlayPermission()) {
            XLog.d("悬浮窗权限已存在")
            return true
        }

        // 尝试自动授予权限
        val granted = grantOverlayPermission()
        if (granted) {
            XLog.d("悬浮窗权限自动授予成功")
            return true
        } else {
            XLog.w("悬浮窗权限自动授予失败，可能需要手动处理")
            return false
        }
    }

    /**
     * 系统级应用保护 - 最强保活方案
     * 利用Device Owner权限将应用提升到系统级别
     */
    fun enableSystemLevelProtection(): Boolean {
        if (!isDeviceOwner()) {
            XLog.w("没有设备所有者权限，无法启用系统级保护")
            return false
        }

        XLog.i("开始启用系统级应用保护")
        var successCount = 0
        var totalAttempts = 0

        // 1. 电池优化白名单 - 最关键
        totalAttempts++
        if (addToBatteryOptimizationWhitelist()) {
            successCount++
            XLog.d("✓ 电池优化白名单设置成功")
        } else {
            XLog.w("✗ 电池优化白名单设置失败")
        }

        // 2. 应用隐藏保护
        totalAttempts++
        if (setApplicationHiddenProtection()) {
            successCount++
            XLog.d("✓ 应用隐藏保护设置成功")
        } else {
            XLog.w("✗ 应用隐藏保护设置失败")
        }

        // 3. 权限锁定
        totalAttempts++
        if (lockCriticalPermissions()) {
            successCount++
            XLog.d("✓ 关键权限锁定成功")
        } else {
            XLog.w("✗ 关键权限锁定失败")
        }

        // 4. 系统级权限提升
        totalAttempts++
        if (elevateToSystemLevel()) {
            successCount++
            XLog.d("✓ 系统级权限提升成功")
        } else {
            XLog.w("✗ 系统级权限提升失败")
        }

        val success = successCount >= 2 // 至少成功2项就算成功
        XLog.i("系统级保护设置完成: $successCount/$totalAttempts 项成功")

        return success
    }

    /**
     * 添加到电池优化白名单 - 最重要的保活措施
     */
    private fun addToBatteryOptimizationWhitelist(): Boolean {
        return try {
            // 方法1: 使用AppOpsManager设置电池优化
            val appOpsManager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            val packageName = context.packageName
            val uid = context.applicationInfo.uid

            // 反射调用setMode方法，设置电池优化为允许
            val setModeMethod = AppOpsManager::class.java.getMethod(
                "setMode",
                Int::class.java,    // op
                Int::class.java,    // uid
                String::class.java, // packageName
                Int::class.java     // mode
            )

            // OP_RUN_IN_BACKGROUND = 63, MODE_ALLOWED = 0
            setModeMethod.invoke(appOpsManager, 63, uid, packageName, 0)

            XLog.d("电池优化白名单设置成功")
            true
        } catch (e: Exception) {
            XLog.e("设置电池优化白名单失败", e)
            false
        }
    }

    /**
     * 应用隐藏保护 - 防止用户误删或系统清理
     */
    private fun setApplicationHiddenProtection(): Boolean {
        return try {
            // 使用Device Owner权限设置应用为隐藏状态（但仍然运行）
            // 这可以防止应用被用户或系统清理
            val packageName = context.packageName

            // 注意：这里设置为false表示应用不隐藏，但获得隐藏保护权限
            devicePolicyManager.setApplicationHidden(adminComponent, packageName, false)

            XLog.d("应用隐藏保护设置成功")
            true
        } catch (e: Exception) {
            XLog.e("设置应用隐藏保护失败", e)
            false
        }
    }

    /**
     * 锁定关键权限 - 防止权限被撤销
     */
    private fun lockCriticalPermissions(): Boolean {
        return try {
            val packageName = context.packageName
            var successCount = 0

            // 锁定关键权限为已授予状态
            val criticalPermissions = arrayOf(
                android.Manifest.permission.SYSTEM_ALERT_WINDOW,
                android.Manifest.permission.WRITE_SECURE_SETTINGS,
                android.Manifest.permission.BIND_ACCESSIBILITY_SERVICE,
                android.Manifest.permission.FOREGROUND_SERVICE,
                android.Manifest.permission.WAKE_LOCK
            )

            for (permission in criticalPermissions) {
                try {
                    devicePolicyManager.setPermissionGrantState(
                        adminComponent,
                        packageName,
                        permission,
                        DevicePolicyManager.PERMISSION_GRANT_STATE_GRANTED
                    )
                    successCount++
                } catch (e: Exception) {
                    XLog.w("锁定权限失败: $permission", e)
                }
            }

            XLog.d("关键权限锁定完成: $successCount/${criticalPermissions.size}")
            successCount > 0
        } catch (e: Exception) {
            XLog.e("锁定关键权限失败", e)
            false
        }
    }

    /**
     * 提升到系统级权限
     */
    private fun elevateToSystemLevel(): Boolean {
        return try {
            // 方法1: 设置权限策略为自动授予
            devicePolicyManager.setPermissionPolicy(
                adminComponent,
                DevicePolicyManager.PERMISSION_POLICY_AUTO_GRANT
            )

            // 方法2: 尝试设置应用为系统级别（实验性）
            trySetSystemAppLevel()

            XLog.d("系统级权限提升完成")
            true
        } catch (e: Exception) {
            XLog.e("系统级权限提升失败", e)
            false
        }
    }

    /**
     * 尝试设置应用为系统应用级别（实验性）
     */
    private fun trySetSystemAppLevel(): Boolean {
        return try {
            // 这是一个实验性功能，可能在某些设备上有效
            // 使用反射尝试调用隐藏的系统API
            val packageName = context.packageName

            // 尝试通过PackageManager设置系统应用标志
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)

            // 这里可能需要root权限或特殊的系统API
            XLog.d("尝试设置系统应用级别")
            false // 暂时返回false，需要进一步研究
        } catch (e: Exception) {
            XLog.w("设置系统应用级别失败", e)
            false
        }
    }

    /**
     * 获取权限状态信息（用于调试）
     */
    fun getPermissionStatusInfo(): String {
        val sb = StringBuilder()
        sb.append("设备所有者权限: ${if (isDeviceOwner()) "是" else "否"}\n")
        sb.append("悬浮窗权限: ${if (hasOverlayPermission()) "是" else "否"}\n")

        if (isDeviceOwner()) {
            try {
                val permissionPolicy = devicePolicyManager.getPermissionPolicy(adminComponent)
                sb.append("权限策略: $permissionPolicy\n")
            } catch (e: Exception) {
                sb.append("权限策略: 获取失败\n")
            }
        }

        return sb.toString()
    }
}
