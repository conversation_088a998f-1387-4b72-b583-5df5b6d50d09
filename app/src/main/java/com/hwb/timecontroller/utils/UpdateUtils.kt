package com.hwb.timecontroller.utils

import android.content.Context
import android.content.pm.PackageManager

/**
 * 更新相关工具类
 */
object UpdateUtils {

    /**
     * 获取应用版本名称
     */
    fun getVersionName(context: Context): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "1.0.0"
        } catch (e: PackageManager.NameNotFoundException) {
            "1.0.0"
        }
    }

    /**
     * 获取应用版本号
     */
    fun getVersionCode(context: Context): Long {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.longVersionCode
        } catch (e: PackageManager.NameNotFoundException) {
            1L
        }
    }

    /**
     * 获取应用包名
     */
    fun getPackageName(context: Context): String {
        return context.packageName
    }
}