package com.hwb.timecontroller.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.hwb.timecontroller.activity.EmptyActivity
import com.hwb.timecontroller.service.AppLifecycleManagerService
import com.hwb.timecontroller.service.FloatingWindowService
import com.hwb.timecontroller.utils.AccessibilityUtils
import com.hwb.timecontroller.utils.DeviceOwnerPermissionManager
import com.elvishew.xlog.XLog

/**
 * 开机启动接收器
 * 负责在设备开机后自动启动应用的核心服务
 * 
 * Author: huangwubin
 * Date: 2025/7/2
 */
class BootReceiver : BroadcastReceiver() {

    companion object {
        private const val STARTUP_DELAY_MS = 3000L // 开机后延迟启动时间
    }

    override fun onReceive(context: Context, intent: Intent) {
        try {
            XLog.d("收到开机广播: ${intent.action}")
            Log.d("开机广播","收到开机广播: ${intent.action}")
            when (intent.action) {
                Intent.ACTION_BOOT_COMPLETED,
                "android.intent.action.QUICKBOOT_POWERON" -> {
                    handleBootCompleted(context)
                }
                Intent.ACTION_MY_PACKAGE_REPLACED,
                Intent.ACTION_PACKAGE_REPLACED -> {
                    // 应用更新后重新启动
                    val packageName = intent.dataString?.removePrefix("package:")
                    if (packageName == context.packageName) {
                        XLog.d("应用更新后重新启动")
                        handleBootCompleted(context)
                    }
                }
            }
        } catch (e: Exception) {
            XLog.e("处理开机广播失败", e)
        }
    }

    /**
     * 处理开机完成事件
     */
    private fun handleBootCompleted(context: Context) {
        try {
            XLog.d("开始处理开机启动逻辑")

            // 延迟启动，确保系统完全启动
            Handler(Looper.getMainLooper()).postDelayed({
                val intent = Intent(context, EmptyActivity::class.java).apply {
                    flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
                }
                context.startActivity(intent)
            }, STARTUP_DELAY_MS)
            
        } catch (e: Exception) {
            XLog.e("处理开机完成事件失败", e)
        }
    }


}
