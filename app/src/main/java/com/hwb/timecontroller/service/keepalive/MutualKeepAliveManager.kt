package com.hwb.timecontroller.service.keepalive

import android.app.ActivityManager
import android.app.Service
import android.content.Context
import android.content.Intent
import com.elvishew.xlog.XLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 互相保活管理器
 * 通过组合模式为各种Service提供互相保活能力
 *
 * Author: huangwubin
 * Date: 2025/7/2
 */
class MutualKeepAliveManager(
    private val context: Context,
    private val currentServiceClass: Class<out Service>,
    private val serviceDisplayName: String
) {

    companion object {
        private const val HEARTBEAT_INTERVAL_MS = 10000L // 心跳间隔10秒
        private const val MONITOR_INTERVAL_MS = 15000L   // 监控间隔15秒
        private const val SERVICE_START_DELAY_MS = 2000L // 服务启动延迟2秒
        private const val MAX_RESTART_ATTEMPTS = 3       // 最大重启尝试次数
    }

    // 协程作用域
    private val keepAliveScope = CoroutineScope(Dispatchers.Main + Job())
    private var heartbeatJob: Job? = null
    private var monitorJob: Job? = null

    // 保活伙伴服务列表
    private val partnerServices = mutableSetOf<Class<out Service>>()

    // 重启尝试计数
    private val restartAttempts = mutableMapOf<Class<out Service>, Int>()

    // 是否正在运行
    private var isRunning = false

    /**
     * 注册需要互相保活的伙伴服务
     */
    fun registerPartnerServices(vararg serviceClasses: Class<out Service>) {
        partnerServices.addAll(serviceClasses)
        XLog.d("[$serviceDisplayName] 注册保活伙伴: ${serviceClasses.map { it.simpleName }}")
    }

    /**
     * 开始保活监控
     */
    fun startKeepAlive() {
        if (isRunning) {
            XLog.w("[$serviceDisplayName] 保活管理器已在运行")
            return
        }

        isRunning = true
        XLog.d("[$serviceDisplayName] 开始保活监控")

        // 注册当前服务到全局注册表
        ServiceRegistry.registerService(currentServiceClass, serviceDisplayName)

        // 启动心跳
        startHeartbeat()

        // 启动监控
        startMonitoring()
    }

    /**
     * 停止保活监控
     */
    fun stopKeepAlive() {
        if (!isRunning) return

        isRunning = false
        XLog.d("[$serviceDisplayName] 停止保活监控")

        // 停止协程
        heartbeatJob?.cancel()
        monitorJob?.cancel()

        // 从注册表移除
        ServiceRegistry.unregisterService(currentServiceClass)
    }

    /**
     * 服务销毁时调用
     */
    fun onServiceDestroyed() {
        stopKeepAlive()
        keepAliveScope.cancel()
    }

    /**
     * 启动心跳机制
     */
    private fun startHeartbeat() {
        heartbeatJob = keepAliveScope.launch {
            while (isRunning) {
                try {
                    // 发送心跳
                    ServiceRegistry.updateHeartbeat(currentServiceClass)
                    // 减少心跳日志输出，只在需要时显示
                    // XLog.v("[$serviceDisplayName] 发送心跳")

                    delay(HEARTBEAT_INTERVAL_MS)
                } catch (e: Exception) {
                    XLog.e("[$serviceDisplayName] 心跳发送失败", e)
                }
            }
        }
    }

    /**
     * 启动服务监控
     */
    private fun startMonitoring() {
        monitorJob = keepAliveScope.launch {
            // 延迟启动监控，让服务完全启动
            delay(SERVICE_START_DELAY_MS)

            while (isRunning) {
                try {
                    checkAndRestartPartnerServices()
                    delay(MONITOR_INTERVAL_MS)
                } catch (e: Exception) {
                    XLog.e("[$serviceDisplayName] 服务监控出错", e)
                }
            }
        }
    }

    /**
     * 检查并重启伙伴服务
     */
    private suspend fun checkAndRestartPartnerServices() {
        for (partnerServiceClass in partnerServices) {
            try {
                val isRunning = isServiceRunning(partnerServiceClass)
                val isAlive = ServiceRegistry.isServiceAlive(partnerServiceClass)

                if (!isRunning || !isAlive) {
                    XLog.w(
                        "运行状态: $isRunning, 心跳状态: $isAlive",
                        "[$serviceDisplayName] 检测到伙伴服务异常: ${partnerServiceClass.simpleName}"
                    )

                    val attempts = restartAttempts.getOrDefault(partnerServiceClass, 0)
                    if (attempts < MAX_RESTART_ATTEMPTS) {
                        restartService(partnerServiceClass)
                        restartAttempts[partnerServiceClass] = attempts + 1
                    } else {
                        XLog.e("[$serviceDisplayName] 伙伴服务 ${partnerServiceClass.simpleName} 重启次数超限，停止重启")
                    }
                } else {
                    // 服务正常，重置重启计数
                    restartAttempts.remove(partnerServiceClass)
                }

            } catch (e: Exception) {
                XLog.e(
                    "[$serviceDisplayName] 检查伙伴服务失败: ${partnerServiceClass.simpleName}",
                    e
                )
            }
        }
    }

    /**
     * 检查服务是否在运行
     */
    private fun isServiceRunning(serviceClass: Class<out Service>): Boolean {
        return try {
            val activityManager =
                context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val services = activityManager.getRunningServices(Integer.MAX_VALUE)

            services.any { serviceInfo ->
                serviceInfo.service.className == serviceClass.name
            }
        } catch (e: Exception) {
            XLog.e("检查服务运行状态失败: ${serviceClass.simpleName}", e)
            false
        }
    }

    /**
     * 重启服务
     */
    private suspend fun restartService(serviceClass: Class<out Service>) {
        try {
            XLog.i("[$serviceDisplayName] 尝试重启伙伴服务: ${serviceClass.simpleName}")

            val intent = Intent(context, serviceClass)
            context.startService(intent)

            // 等待服务启动
            delay(SERVICE_START_DELAY_MS)

            XLog.i("[$serviceDisplayName] 伙伴服务重启完成: ${serviceClass.simpleName}")

        } catch (e: Exception) {
            XLog.e("[$serviceDisplayName] 重启伙伴服务失败: ${serviceClass.simpleName}", e)
        }
    }

    /**
     * 获取保活状态信息
     */
    fun getKeepAliveStatus(): String {
        val sb = StringBuilder()
        sb.append("=== $serviceDisplayName 保活状态 ===\n")
        sb.append("运行状态: ${if (isRunning) "运行中" else "已停止"}\n")
        sb.append("伙伴服务数量: ${partnerServices.size}\n")

        for (partnerServiceClass in partnerServices) {
            val isRunning = isServiceRunning(partnerServiceClass)
            val isAlive = ServiceRegistry.isServiceAlive(partnerServiceClass)
            val attempts = restartAttempts.getOrDefault(partnerServiceClass, 0)

            sb.append("- ${partnerServiceClass.simpleName}: 运行=$isRunning, 心跳=$isAlive, 重启次数=$attempts\n")
        }

        return sb.toString()
    }
}
