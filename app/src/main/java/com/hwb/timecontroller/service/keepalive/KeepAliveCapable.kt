package com.hwb.timecontroller.service.keepalive

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import androidx.core.app.NotificationCompat
import com.elvishew.xlog.XLog
import com.hwb.timecontroller.R
import com.hwb.timecontroller.activity.MainActivity

/**
 * 保活能力接口
 * 为Service提供统一的保活接口和前台服务管理
 *
 * Author: huangwubin
 * Date: 2025/7/2
 */
interface KeepAliveCapable {

    companion object {
        // 前台服务通知相关常量
        private const val DEFAULT_CHANNEL_ID = "keep_alive_service_channel"
        private const val DEFAULT_NOTIFICATION_ID = 1000
    }
    
    /**
     * 获取保活管理器实例
     */
    fun getKeepAliveManager(): MutualKeepAliveManager
    
    /**
     * 当伙伴服务死亡时的回调
     */
    fun onPartnerServiceDied(serviceClass: Class<out Service>) {
        // 默认实现：记录日志
        XLog.w("伙伴服务死亡: ${serviceClass.simpleName}")
    }
    
    /**
     * 当伙伴服务重启时的回调
     */
    fun onPartnerServiceRestarted(serviceClass: Class<out Service>) {
        // 默认实现：记录日志
        XLog.i("伙伴服务重启: ${serviceClass.simpleName}")
    }
    
    /**
     * 获取保活状态信息
     */
    fun getKeepAliveStatus(): String {
        return getKeepAliveManager().getKeepAliveStatus()
    }
    
    /**
     * 设置保活配置的便捷方法
     * 简化保活管理器的初始化和启动过程
     */
    fun setupKeepAlive(
        context: android.content.Context,
        currentServiceClass: Class<out Service>,
        serviceDisplayName: String,
        vararg partnerServices: Class<out Service>
    ): MutualKeepAliveManager {
        val manager = MutualKeepAliveManager(context, currentServiceClass, serviceDisplayName)
        manager.registerPartnerServices(*partnerServices)
        manager.startKeepAlive()
        return manager
    }

    /**
     * 启动前台服务的统一方法
     * 自动处理通知渠道创建和前台服务启动
     *
     * @param service 服务实例
     * @param serviceName 服务显示名称
     * @param notificationId 通知ID（可选，默认使用服务类名hash）
     * @param channelId 通知渠道ID（可选，默认使用服务类名）
     */
    fun startForegroundServiceSafely(
        service: Service,
        serviceName: String,
        notificationId: Int = service.javaClass.simpleName.hashCode().let { if (it < 0) -it else it } + DEFAULT_NOTIFICATION_ID,
        channelId: String = "${DEFAULT_CHANNEL_ID}_${service.javaClass.simpleName.lowercase()}"
    ) {
        var foregroundStarted = false

        try {
            val notificationManager = service.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 创建通知渠道
            createNotificationChannel(notificationManager, channelId, serviceName)

            // 创建并启动前台服务通知
            val notification = createServiceNotification(service, serviceName, channelId)
            service.startForeground(notificationId, notification)
            foregroundStarted = true

            XLog.d("前台服务启动成功: $serviceName (ID: $notificationId)")

        } catch (e: Exception) {
            XLog.e("启动前台服务失败: $serviceName，尝试最简方案", e)

            // 如果前台服务还没启动，使用最简方案确保启动
            if (!foregroundStarted) {
                try {
                    // 最简通知方案
                    val fallbackNotification = android.app.Notification.Builder(service, channelId)
                        .setContentTitle("时间控制器")
                        .setContentText("${serviceName}运行中")
                        .setSmallIcon(android.R.drawable.ic_dialog_info)
                        .build()

                    service.startForeground(notificationId, fallbackNotification)
                    XLog.d("前台服务最简方案启动成功: $serviceName")
                } catch (fallbackException: Exception) {
                    XLog.e("前台服务最简方案也失败: $serviceName", fallbackException)
                    throw e // 抛出原始异常
                }
            } else {
                throw e // 如果前台服务已启动，抛出原始异常
            }
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel(
        notificationManager: NotificationManager,
        channelId: String,
        serviceName: String
    ) {
        val channel = NotificationChannel(
            channelId,
            serviceName,
            NotificationManager.IMPORTANCE_LOW
        ).apply {
            description = "${serviceName}正在运行"
            setShowBadge(false)
        }
        notificationManager.createNotificationChannel(channel)
    }

    /**
     * 创建服务通知
     */
    private fun createServiceNotification(
        service: Service,
        serviceName: String,
        channelId: String
    ): Notification {
        return try {
            // 尝试创建带PendingIntent的完整通知
            val intent = Intent(service, MainActivity::class.java)
            val pendingIntent = PendingIntent.getActivity(
                service, 0, intent, PendingIntent.FLAG_IMMUTABLE
            )

            NotificationCompat.Builder(service, channelId)
                .setContentTitle("时间控制器")
                .setContentText("${serviceName}正在运行")
                .setSmallIcon(android.R.drawable.ic_dialog_info) // 使用系统默认图标
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .build()
        } catch (e: Exception) {
            XLog.w("创建完整通知失败，使用简化通知", e)
            // 备用方案：创建简化通知
            NotificationCompat.Builder(service, channelId)
                .setContentTitle("时间控制器")
                .setContentText("${serviceName}正在运行")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setOngoing(true)
                .build()
        }
    }
}
