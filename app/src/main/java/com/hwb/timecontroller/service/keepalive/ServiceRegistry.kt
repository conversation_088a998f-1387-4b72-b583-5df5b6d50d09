package com.hwb.timecontroller.service.keepalive

import android.app.Service
import com.elvishew.xlog.XLog
import java.util.concurrent.ConcurrentHashMap

/**
 * 服务注册表
 * 管理所有参与保活的服务状态
 * 
 * Author: huangwubin
 * Date: 2025/7/2
 */
object ServiceRegistry {

    private const val HEARTBEAT_TIMEOUT_MS = 30000L // 心跳超时30秒

    // 服务状态数据类
    data class ServiceState(
        val serviceClass: Class<out Service>,
        val displayName: String,
        val registeredTime: Long,
        var lastHeartbeat: Long,
        var isActive: Boolean = true
    )
    
    // 注册的服务状态
    private val serviceStates = ConcurrentHashMap<Class<out Service>, ServiceState>()
    
    /**
     * 注册服务
     */
    fun registerService(serviceClass: Class<out Service>, displayName: String) {
        val currentTime = System.currentTimeMillis()
        val state = ServiceState(
            serviceClass = serviceClass,
            displayName = displayName,
            registeredTime = currentTime,
            lastHeartbeat = currentTime,
            isActive = true
        )
        
        serviceStates[serviceClass] = state
        XLog.d("服务注册: $displayName (${serviceClass.simpleName})")
    }
    
    /**
     * 注销服务
     */
    fun unregisterService(serviceClass: Class<out Service>) {
        val state = serviceStates.remove(serviceClass)
        if (state != null) {
            XLog.d("服务注销: ${state.displayName} (${serviceClass.simpleName})")
        }
    }
    
    /**
     * 更新服务心跳
     */
    fun updateHeartbeat(serviceClass: Class<out Service>) {
        val state = serviceStates[serviceClass]
        if (state != null) {
            state.lastHeartbeat = System.currentTimeMillis()
            state.isActive = true
            // 减少心跳日志输出，只在需要时显示
            // XLog.v("更新心跳: ${state.displayName}")
        }
    }
    
    /**
     * 检查服务是否存活（基于心跳）
     */
    fun isServiceAlive(serviceClass: Class<out Service>): Boolean {
        val state = serviceStates[serviceClass] ?: return false
        val currentTime = System.currentTimeMillis()
        val timeSinceLastHeartbeat = currentTime - state.lastHeartbeat
        
        return timeSinceLastHeartbeat <= HEARTBEAT_TIMEOUT_MS
    }
    
    /**
     * 获取所有已死亡的服务
     */
    fun getDeadServices(): List<Class<out Service>> {
        val currentTime = System.currentTimeMillis()
        return serviceStates.values
            .filter { state ->
                val timeSinceLastHeartbeat = currentTime - state.lastHeartbeat
                timeSinceLastHeartbeat > HEARTBEAT_TIMEOUT_MS
            }
            .map { it.serviceClass }
    }
    
    /**
     * 获取所有注册的服务
     */
    fun getAllRegisteredServices(): List<Class<out Service>> {
        return serviceStates.keys.toList()
    }
    
    /**
     * 获取除指定服务外的所有其他服务
     */
    fun getPartnerServices(excludeServiceClass: Class<out Service>): List<Class<out Service>> {
        return serviceStates.keys.filter { it != excludeServiceClass }
    }
    
    /**
     * 获取服务状态
     */
    fun getServiceState(serviceClass: Class<out Service>): ServiceState? {
        return serviceStates[serviceClass]
    }
    
    /**
     * 标记服务为非活跃状态
     */
    fun markServiceInactive(serviceClass: Class<out Service>) {
        val state = serviceStates[serviceClass]
        if (state != null) {
            state.isActive = false
            XLog.w("标记服务为非活跃: ${state.displayName}")
        }
    }
    
    /**
     * 获取所有服务状态的详细信息
     */
    fun getDetailedStatus(): String {
        val sb = StringBuilder()
        val currentTime = System.currentTimeMillis()
        
        sb.append("=== 服务注册表状态 ===\n")
        sb.append("注册服务数量: ${serviceStates.size}\n\n")
        
        if (serviceStates.isEmpty()) {
            sb.append("无注册服务\n")
        } else {
            serviceStates.values.forEach { state ->
                val timeSinceLastHeartbeat = currentTime - state.lastHeartbeat
                val isAlive = timeSinceLastHeartbeat <= HEARTBEAT_TIMEOUT_MS
                
                sb.append("服务: ${state.displayName}\n")
                sb.append("  类名: ${state.serviceClass.simpleName}\n")
                sb.append("  注册时间: ${formatTime(state.registeredTime)}\n")
                sb.append("  最后心跳: ${formatTime(state.lastHeartbeat)}\n")
                sb.append("  心跳间隔: ${timeSinceLastHeartbeat}ms\n")
                sb.append("  存活状态: ${if (isAlive) "存活" else "死亡"}\n")
                sb.append("  活跃状态: ${if (state.isActive) "活跃" else "非活跃"}\n")
                sb.append("\n")
            }
        }
        
        return sb.toString()
    }
    
    /**
     * 清理所有注册信息（用于测试或重置）
     */
    fun clearAll() {
        XLog.w("清理所有服务注册信息")
        serviceStates.clear()
    }
    
    /**
     * 格式化时间显示
     */
    private fun formatTime(timestamp: Long): String {
        val date = java.util.Date(timestamp)
        val format = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
        return format.format(date)
    }
    
    /**
     * 获取服务运行时长
     */
    fun getServiceUptime(serviceClass: Class<out Service>): Long {
        val state = serviceStates[serviceClass] ?: return 0L
        return System.currentTimeMillis() - state.registeredTime
    }
    
    /**
     * 检查是否有任何服务注册
     */
    fun hasAnyService(): Boolean {
        return serviceStates.isNotEmpty()
    }
    
    /**
     * 获取活跃服务数量
     */
    fun getActiveServiceCount(): Int {
        val currentTime = System.currentTimeMillis()
        return serviceStates.values.count { state ->
            val timeSinceLastHeartbeat = currentTime - state.lastHeartbeat
            timeSinceLastHeartbeat <= HEARTBEAT_TIMEOUT_MS && state.isActive
        }
    }
}
