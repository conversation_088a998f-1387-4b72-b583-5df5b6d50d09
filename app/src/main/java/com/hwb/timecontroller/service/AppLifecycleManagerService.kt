package com.hwb.timecontroller.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.Settings
import androidx.core.app.NotificationCompat
import com.hwb.timecontroller.service.keepalive.KeepAliveCapable
import com.hwb.timecontroller.service.keepalive.MutualKeepAliveManager
import com.hwb.timecontroller.utils.AccessibilityUtils
import com.hwb.timecontroller.utils.DeviceOwnerPermissionManager
import com.elvishew.xlog.XLog

/**
 * 应用生命周期管理服务
 * 负责智能启动逻辑判断和服务保活管理
 *
 * Author: huangwubin
 * Date: 2025/7/2
 */
class AppLifecycleManagerService : Service(), KeepAliveCapable {

    companion object {
        private const val STARTUP_CHECK_DELAY_MS = 2000L // 启动检查延迟
        private const val PERMISSION_RECHECK_DELAY_MS = 5000L // 权限重新检查延迟
        
        /**
         * 启动生命周期管理服务
         */
        fun start(context: Context) {
            val intent = Intent(context, AppLifecycleManagerService::class.java)
            try {
                // Android 8.0+ 需要使用前台服务
                context.startForegroundService(intent)
            } catch (e: Exception) {
                XLog.e("启动AppLifecycleManagerService失败", e)
            }
        }
    }

    private lateinit var deviceOwnerManager: DeviceOwnerPermissionManager
    private lateinit var accessibilityUtils: AccessibilityUtils
    private lateinit var keepAliveManager: MutualKeepAliveManager
    private val handler = Handler(Looper.getMainLooper())

    // 标记MainActivity是否应该启动
    private var shouldStartMainActivity = true

    override fun onCreate() {
        super.onCreate()

        XLog.d("应用生命周期管理服务开始创建")

        // 使用统一的前台服务启动方法，添加异常处理确保前台服务一定启动
        try {
            startForegroundServiceSafely(this, "应用生命周期管理服务")
        } catch (e: Exception) {
            XLog.e("统一前台服务启动失败", e)
        }

        // 初始化保活管理器
        keepAliveManager = MutualKeepAliveManager(
            context = this,
            currentServiceClass = AppLifecycleManagerService::class.java,
            serviceDisplayName = "应用生命周期管理服务"
        )

        // 注册需要保活的伙伴服务
        keepAliveManager.registerPartnerServices(
            CountdownService::class.java,
            FloatingWindowService::class.java,
            AppLifecycleAccessibilityService::class.java,
            CheckService::class.java
        )

        // 启动保活
        keepAliveManager.startKeepAlive()

        // 初始化工具类
        deviceOwnerManager = DeviceOwnerPermissionManager(this)
        accessibilityUtils = AccessibilityUtils(this)

        // 启用系统级保护（Device Owner权限）
        enableSystemLevelProtection()

        // 启动备用保活作业（最后防线）
        BackupKeepAliveJobService.startBackupKeepAlive(this)

        // 延迟执行智能启动逻辑
        handler.postDelayed({
            performSmartStartup()
        }, STARTUP_CHECK_DELAY_MS)

        XLog.d("应用生命周期管理服务创建完成")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // 每次收到启动命令都重新执行智能启动逻辑
        handler.postDelayed({
            performSmartStartup()
        }, STARTUP_CHECK_DELAY_MS)

        // 确保保活管理器正在运行
        if (::keepAliveManager.isInitialized) {
            keepAliveManager.startKeepAlive()
        }

        return START_STICKY
    }

    override fun onDestroy() {
        try {
            XLog.d("应用生命周期管理服务开始销毁")

            // 停止保活
            if (::keepAliveManager.isInitialized) {
                keepAliveManager.onServiceDestroyed()
            }

            XLog.d("应用生命周期管理服务销毁完成")

        } catch (e: Exception) {
            XLog.e("应用生命周期管理服务销毁失败", e)
        } finally {
            super.onDestroy()
        }
    }

    /**
     * 执行智能启动逻辑
     */
    private fun performSmartStartup() {
        try {
            XLog.d("开始执行智能启动逻辑")
            
            val hasDeviceOwner = deviceOwnerManager.isDeviceOwner()
            val hasAccessibility = accessibilityUtils.isAccessibilityServiceEnabled()
            val hasOverlay = Settings.canDrawOverlays(this)
            
            XLog.d("权限状态 - 设备所有者: $hasDeviceOwner, 无障碍: $hasAccessibility, 悬浮窗: $hasOverlay")
            
            when {
                hasDeviceOwner && hasAccessibility && hasOverlay -> {
                    // 场景1：完整权限，直接启动悬浮窗，不启动MainActivity
                    handleScenario1()
                }
                hasDeviceOwner && !hasAccessibility -> {
                    // 场景2：有设备所有者但无无障碍权限，启动MainActivity
                    handleScenario2()
                }
                hasDeviceOwner && hasAccessibility && !hasOverlay -> {
                    // 场景3：有设备所有者和无障碍，但无悬浮窗权限，自动获取
                    handleScenario3()
                }
                else -> {
                    // 其他情况：启动MainActivity
                    handleOtherScenarios()
                }
            }
            
        } catch (e: Exception) {
            XLog.e("智能启动逻辑执行失败", e)
            // 失败时默认启动MainActivity
            startMainActivity()
        }
    }
    
    /**
     * 场景1：完整权限，直接启动悬浮窗
     */
    private fun handleScenario1() {
        XLog.i("场景1：完整权限，直接启动悬浮窗服务和CheckService")
        shouldStartMainActivity = false

        try {
            // 启动CheckService
            CheckService.start(this)
            XLog.d("CheckService启动成功")

            // 启动悬浮窗服务
            FloatingWindowService.start(this)
            XLog.d("悬浮窗服务启动成功")
        } catch (e: Exception) {
            XLog.e("服务启动失败，降级到MainActivity", e)
            startMainActivity()
        }
    }
    
    /**
     * 场景2：有设备所有者但无无障碍权限
     */
    private fun handleScenario2() {
        XLog.i("场景2：有设备所有者但无无障碍权限，启动MainActivity")
        shouldStartMainActivity = true
        startMainActivity()
    }
    
    /**
     * 场景3：有设备所有者和无障碍，但无悬浮窗权限
     */
    private fun handleScenario3() {
        XLog.i("场景3：自动获取悬浮窗权限")
        shouldStartMainActivity = false

        try {
            // 先启动CheckService，无论悬浮窗权限如何
            CheckService.start(this)
            XLog.d("CheckService启动成功")

            // 尝试自动授予悬浮窗权限
            val granted = deviceOwnerManager.grantOverlayPermission()

            if (granted) {
                XLog.d("悬浮窗权限自动获取成功，启动悬浮窗服务")
                FloatingWindowService.start(this)
            } else {
                XLog.w("悬浮窗权限自动获取失败，通过无障碍服务获取")
                // 通过无障碍服务自动获取权限
                triggerAccessibilityToGetOverlayPermission()
            }

        } catch (e: Exception) {
            XLog.e("场景3处理失败，降级到MainActivity", e)
            startMainActivity()
        }
    }
    
    /**
     * 其他场景：启动MainActivity
     */
    private fun handleOtherScenarios() {
        XLog.i("其他场景：启动MainActivity")
        shouldStartMainActivity = true
        startMainActivity()
    }
    
    /**
     * 启动MainActivity
     */
    private fun startMainActivity() {
        try {
            val intent = Intent(this, com.hwb.timecontroller.activity.MainActivity::class.java)
            // 移除FLAG_ACTIVITY_CLEAR_TOP避免影响EmptyActivity的startLockTask状态
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP
            startActivity(intent)
            XLog.d("MainActivity启动成功")
        } catch (e: Exception) {
            XLog.e("MainActivity启动失败", e)
        }
    }
    
    /**
     * 启用系统级保护
     */
    private fun enableSystemLevelProtection() {
        try {
            if (deviceOwnerManager.isDeviceOwner()) {
                XLog.i("检测到Device Owner权限，启用系统级应用保护")
                val success = deviceOwnerManager.enableSystemLevelProtection()
                if (success) {
                    XLog.i("✓ 系统级应用保护启用成功，应用获得更强的进程保护")
                } else {
                    XLog.w("✗ 系统级应用保护启用失败，将使用标准保活机制")
                }
            } else {
                XLog.d("没有Device Owner权限，跳过系统级保护")
            }
        } catch (e: Exception) {
            XLog.e("启用系统级保护失败", e)
        }
    }

    /**
     * 触发无障碍服务获取悬浮窗权限
     */
    private fun triggerAccessibilityToGetOverlayPermission() {
        try {
            XLog.d("触发无障碍服务获取悬浮窗权限")

            // 这里可以通过广播或其他方式通知无障碍服务
            // 暂时使用延迟重新检查的方式
            handler.postDelayed({
                recheckPermissionsAndStartServices()
            }, PERMISSION_RECHECK_DELAY_MS)

        } catch (e: Exception) {
            XLog.e("触发无障碍服务获取权限失败", e)
        }
    }
    
    /**
     * 重新检查权限并启动服务
     */
    private fun recheckPermissionsAndStartServices() {
        try {
            val hasOverlay = Settings.canDrawOverlays(this)
            
            if (hasOverlay) {
                XLog.d("权限重新检查：悬浮窗权限已获取，启动悬浮窗服务")
                FloatingWindowService.start(this)
            } else {
                XLog.w("权限重新检查：悬浮窗权限仍未获取，启动MainActivity")
                startMainActivity()
            }
            
        } catch (e: Exception) {
            XLog.e("重新检查权限失败", e)
            startMainActivity()
        }
    }
    
    /**
     * 检查MainActivity是否应该启动
     */
    fun shouldMainActivityStart(): Boolean {
        return shouldStartMainActivity
    }
    
    /**
     * 设置MainActivity启动标志
     */
    fun setShouldStartMainActivity(should: Boolean) {
        shouldStartMainActivity = should
        XLog.d("设置MainActivity启动标志: $should")
    }


    override fun onBind(intent: Intent?): IBinder? = null

    // 实现KeepAliveCapable接口
    override fun getKeepAliveManager(): MutualKeepAliveManager {
        return keepAliveManager
    }

    override fun onPartnerServiceDied(serviceClass: Class<out Service>) {
        super.onPartnerServiceDied(serviceClass)
        XLog.w("生命周期管理服务检测到伙伴服务死亡: ${serviceClass.simpleName}")
    }

    override fun onPartnerServiceRestarted(serviceClass: Class<out Service>) {
        super.onPartnerServiceRestarted(serviceClass)
        XLog.i("生命周期管理服务检测到伙伴服务重启: ${serviceClass.simpleName}")
    }
}
