package com.hwb.timecontroller.constant

/**
 * 用户存储相关常量
 * 
 * Author: huang<PERSON>bin
 * Date: 2025/7/4
 */
object UserStorageKeys {
    
    /**
     * MMKV存储键名
     */
    object MMKV {
        /**
         * 设备UUID（UUIDv7格式）
         */
        const val USER_DEVICE_UUID = "user_device_uuid"

        /**
         * 设备UUID生成时间戳
         */
        const val DEVICE_UUID_GENERATED_TIME = "device_uuid_generated_time"
        
        /**
         * 设备UUID备份状态（是否已备份到MediaStore）
         */
        const val DEVICE_UUID_BACKUP_STATUS = "device_uuid_backup_status"
    }
    
    /**
     * MediaStore文件存储相关
     */
    object MediaStore {
        /**
         * 设备UUID备份文件名
         */
        const val DEVICE_UUID_FILE_NAME = "device_uuid.txt"
        
        /**
         * 设备UUID备份文件MIME类型
         */
        const val DEVICE_UUID_FILE_MIME_TYPE = "text/plain"
    }
    
    /**
     * 用户管理相关配置
     */
    object Config {
        /**
         * 用户活跃状态更新间隔（毫秒）- 5分钟
         * 避免频繁更新lastActiveTime
         */
        const val USER_ACTIVE_UPDATE_INTERVAL_MS = 5 * 60 * 1000L
    }
    
}
