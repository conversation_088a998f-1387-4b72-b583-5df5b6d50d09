package com.hwb.timecontroller.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import android.view.WindowManager
import android.view.inputmethod.EditorInfo
import android.widget.Button
import android.widget.EditText
import android.widget.Toast
import com.hwb.timecontroller.R
import com.hwb.timecontroller.constant.Constant
import com.elvishew.xlog.XLog

/**
 * 透明的密码输入对话框Activity
 * 专门用于在悬浮窗点击时显示密码输入对话框
 * 使用原生布局确保软键盘能够正常弹出和处理
 */
class PasswordDialogActivity : Activity() {

    private lateinit var etPassword: EditText
    private lateinit var btnConfirm: Button
    private lateinit var btnCancel: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置布局
        setContentView(R.layout.activity_password_dialog)

        // 设置软键盘处理模式，确保对话框不被顶到屏幕顶部
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)

        // 设置窗口标志，确保可以正确显示在其他应用之上
        window.addFlags(
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
        )

        // 初始化视图
        initViews()

        // 设置监听器
        setupListeners()

        // 自动弹出软键盘
        showSoftKeyboard()
    }

    private fun initViews() {
        etPassword = findViewById(R.id.et_password)
        btnConfirm = findViewById(R.id.btn_confirm)
        btnCancel = findViewById(R.id.btn_cancel)
    }

    private fun setupListeners() {
        // 确定按钮点击事件
        btnConfirm.setOnClickListener {
            validatePassword()
        }

        // 取消按钮点击事件
        btnCancel.setOnClickListener {
            finish()
        }

        // 输入框回车事件
        etPassword.setOnEditorActionListener { _, actionId, event ->
            if (actionId == EditorInfo.IME_ACTION_DONE ||
                (event != null && event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN)
            ) {
                validatePassword()
                true
            } else {
                false
            }
        }

        // 点击外部区域取消 - 设置根布局的点击事件
        val rootLayout = findViewById<android.view.View>(R.id.root_layout)
        rootLayout?.setOnClickListener {
            // 点击对话框外部关闭
            finish()
        }

        // 防止对话框内部点击事件冒泡到根布局
        val dialogContainer = findViewById<android.view.View>(R.id.dialog_container)
        dialogContainer?.setOnClickListener {
            // 阻止事件冒泡，不关闭对话框
        }
    }

    private fun showSoftKeyboard() {
        etPassword.requestFocus()
        etPassword.postDelayed({
            val imm =
                getSystemService(INPUT_METHOD_SERVICE) as android.view.inputmethod.InputMethodManager
            imm.showSoftInput(etPassword, android.view.inputmethod.InputMethodManager.SHOW_IMPLICIT)
        }, 100)
    }

    private fun validatePassword() {
        val inputPassword = etPassword.text.toString().trim()

        if (inputPassword.isEmpty()) {
            Toast.makeText(this, "请输入密码", Toast.LENGTH_SHORT).show()
            return
        }

        if (inputPassword == Constant.DEF_SUPER_ADMIN_PWD) {
            XLog.d("密码验证成功，打开MainActivity")
            // 密码验证成功后打开MainActivity
            openMainActivity()
            finish()
        } else {
            Toast.makeText(this, "密码错误", Toast.LENGTH_SHORT).show()
            // 清空输入框并重新获取焦点
            etPassword.setText("")
            etPassword.requestFocus()
        }
    }

    private fun openMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        XLog.d("打开MainActivity")
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, 0)
    }

    override fun onBackPressed() {
        // 按返回键也关闭对话框
        super.onBackPressed()
    }
}
