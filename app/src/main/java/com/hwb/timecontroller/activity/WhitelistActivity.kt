package com.hwb.timecontroller.activity

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import android.widget.Toast
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.hwb.timecontroller.R
import com.hwb.timecontroller.business.AppInfo
import com.hwb.timecontroller.business.WhitelistManager
import com.hwb.timecontroller.viewModel.CountdownTimeViewModel

/**
 * 白名单管理Activity
 * 用于管理允许使用的应用白名单
 */
class WhitelistActivity : AppCompatActivity() {

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: AppListAdapter
    private val viewmodel by viewModels<CountdownTimeViewModel>()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_whitelist)

        supportActionBar?.title = "应用白名单管理"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        initViews()
        loadApps()
    }
    
    private fun initViews() {
        recyclerView = findViewById(R.id.rv_apps)
        recyclerView.layoutManager = LinearLayoutManager(this)
        
        adapter = AppListAdapter { appInfo, isChecked ->
            if (isChecked) {
                WhitelistManager.addToWhitelist(appInfo.packageName, this)
                Toast.makeText(this, "${appInfo.appName} 已添加到白名单", Toast.LENGTH_SHORT).show()
            } else {
                WhitelistManager.removeFromWhitelist(appInfo.packageName, this)
                Toast.makeText(this, "${appInfo.appName} 已从白名单移除", Toast.LENGTH_SHORT).show()
            }
        }
        
        recyclerView.adapter = adapter
    }
    
    private fun loadApps() {
        val apps = WhitelistManager.getAllInstalledApps(this)
        adapter.updateApps(apps)
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}

/**
 * 应用列表适配器
 */
class AppListAdapter(
    private val onItemCheckedChange: (AppInfo, Boolean) -> Unit
) : RecyclerView.Adapter<AppListAdapter.ViewHolder>() {
    
    private var apps: List<AppInfo> = emptyList()
    
    fun updateApps(newApps: List<AppInfo>) {
        apps = newApps
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_app_whitelist, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(apps[position])
    }
    
    override fun getItemCount(): Int = apps.size
    
    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvAppName: TextView = itemView.findViewById(R.id.tv_app_name)
        private val tvPackageName: TextView = itemView.findViewById(R.id.tv_package_name)
        private val cbWhitelist: CheckBox = itemView.findViewById(R.id.cb_whitelist)
        
        fun bind(appInfo: AppInfo) {
            tvAppName.text = appInfo.appName
            tvPackageName.text = appInfo.packageName
            
            // 设置复选框状态，但不触发监听器
            cbWhitelist.setOnCheckedChangeListener(null)
            cbWhitelist.isChecked = appInfo.isInWhitelist
            
            // 重新设置监听器
            cbWhitelist.setOnCheckedChangeListener { _, isChecked ->
                onItemCheckedChange(appInfo, isChecked)
            }
            
            // 整个item点击也可以切换状态
            itemView.setOnClickListener {
                cbWhitelist.isChecked = !cbWhitelist.isChecked
            }
        }
    }
}
