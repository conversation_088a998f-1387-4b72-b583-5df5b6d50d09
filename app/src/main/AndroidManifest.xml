<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.hwb.timecontroller">

    <!-- 权限声明 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission
        android:name="android.permission.WRITE_SECURE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.BIND_ACCESSIBILITY_SERVICE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <!-- 开机自启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- XUpdate相关权限 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:testOnly="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.TimeController"
        android:networkSecurityConfig="@xml/network_security_config">

        <!-- 启动页Activity -->
        <activity
            android:name=".activity.EmptyActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/Theme.Splash"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 主Activity -->
        <activity
            android:name=".activity.MainActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:excludeFromRecents="${excludeFromRecents}" />

        <!-- 锁定Activity -->
        <activity
            android:name=".activity.LoginActivity"
            android:exported="false"
            android:launchMode="singleTask"/>

        <!-- 用户信息Activity -->
        <activity
            android:name=".activity.UserInfoActivity"
            android:exported="false"
            android:launchMode="singleTop"/>

        <!-- 白名单管理Activity -->
        <activity
            android:name=".activity.WhitelistActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 应用启动验证Activity -->
        <activity
            android:name=".activity.CheckWhenLauncherActivity"
            android:exported="false"
            android:theme="@style/Theme.Transparent"
            android:excludeFromRecents="true"
            android:launchMode="singleTop" />

        <!-- 应用数据清理Activity -->
        <activity
            android:name=".activity.AppDataCleanupActivity"
            android:exported="false"
            android:theme="@style/Theme.Transparent"
            android:excludeFromRecents="true"
            android:launchMode="singleTop" />

        <!-- 密码对话框Activity -->
        <activity
            android:name=".activity.PasswordDialogActivity"
            android:exported="false"
            android:theme="@style/Theme.Transparent"
            android:excludeFromRecents="true"
            android:taskAffinity=""
            android:launchMode="singleTop"
            android:windowSoftInputMode="adjustResize" />

        <!-- 倒计时服务 -->
        <service
            android:name=".service.CountdownService"
            android:exported="false"
            android:foregroundServiceType="specialUse">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="countdown_timer" />
        </service>

        <!-- 悬浮窗服务 -->
        <service
            android:name=".service.FloatingWindowService"
            android:exported="false" />

        <!-- 应用生命周期管理服务 -->
        <service
            android:name=".service.AppLifecycleManagerService"
            android:exported="false" />

        <!-- 应用启动权限验证服务 -->
        <service
            android:name=".service.CheckService"
            android:exported="true"
            android:foregroundServiceType="specialUse">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="app_launch_verification" />
            <intent-filter>
                <action android:name="com.hwb.timecontroller.service.ICheckService" />
            </intent-filter>
        </service>

        <!-- 备用保活作业服务 -->
        <service
            android:name=".service.BackupKeepAliveJobService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <!-- 设备管理接收器 -->
        <receiver
            android:name=".AppDeviceAdminReceiver"
            android:exported="true"
            android:permission="android.permission.BIND_DEVICE_ADMIN">
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin" />
            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
            </intent-filter>
        </receiver>

        <!-- 无障碍服务 -->
        <service
            android:name=".service.AppLifecycleAccessibilityService"
            android:exported="false"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility_service_config" />
        </service>

        <!-- 开机启动接收器 -->
        <!-- 开机启动接收器 -->
        <receiver
            android:name="com.hwb.timecontroller.receiver.BootReceiver"
            android:enabled="true"
            android:exported="true">

            <!-- 开机广播 -->
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <!-- 包替换广播 -->
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>
    </application>

</manifest>