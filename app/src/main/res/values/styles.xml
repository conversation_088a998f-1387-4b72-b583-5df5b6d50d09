<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- LockActivity 专用样式 -->

    <!-- 根样式定义 -->
    <style name="LockActivity">
        <!-- 基础样式，作为所有LockActivity样式的根 -->
    </style>

    <!-- 基础样式定义 -->
    <style name="LockActivity.Title">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/lock_text_primary</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="LockActivity.Text">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/lock_text_primary</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="LockActivity.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/lock_on_primary</item>
    </style>

    <!-- 主标题样式 -->
    <style name="LockActivity.Title.Primary">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/lock_text_primary</item>
        <item name="android:shadowColor">@color/lock_shadow_strong</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">3</item>
        <item name="android:shadowRadius">6</item>
        <item name="android:letterSpacing">0.02</item>
    </style>
    
    <!-- 副标题样式 -->
    <style name="LockActivity.Title.Secondary">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/lock_text_accent</item>
        <item name="android:shadowColor">@color/lock_shadow_soft</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">4</item>
        <item name="android:letterSpacing">0.01</item>
    </style>
    
    <!-- 状态文字样式 -->
    <style name="LockActivity.Text.Status">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/lock_text_accent</item>
        <item name="android:shadowColor">@color/lock_shadow_soft</item>
        <item name="android:shadowDx">0</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">4</item>
        <item name="android:letterSpacing">0.01</item>
    </style>
    
    <!-- 说明文字样式 -->
    <style name="LockActivity.Text.Description">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/lock_text_secondary</item>
        <item name="android:lineSpacingMultiplier">1.3</item>
        <item name="android:alpha">0.9</item>
    </style>
    
    <!-- 按钮文字样式 -->
    <style name="LockActivity.Button.Text">
        <item name="android:textSize">15sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.01</item>
        <item name="android:elevation">0dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
</resources>
