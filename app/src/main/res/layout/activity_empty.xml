<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.EmptyActivity">

    <!-- 应用名称 -->
    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/home_app_title"
        android:textColor="@color/white"
        android:textSize="32sp"
        android:textStyle="bold"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintBottom_toTopOf="@+id/progress_loading"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progress_loading"
        style="?android:attr/progressBarStyle"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginTop="32dp"
        android:indeterminateTint="@color/white"
        app:layout_constraintBottom_toTopOf="@+id/tv_loading_text"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_app_name" />

    <!-- 加载文字 -->
    <TextView
        android:id="@+id/tv_loading_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/app_loading"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:alpha="0.8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progress_loading" />

    <!-- 版本信息（可选） -->
    <TextView
        android:id="@+id/tv_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        android:text="v1.0.0"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:alpha="0.6"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
