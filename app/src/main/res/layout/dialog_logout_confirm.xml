<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_userinfo_exit_dialog"
    android:orientation="vertical"
    android:paddingHorizontal="25dp"
    android:paddingVertical="15dp">

    <!-- 标题 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:text="退出登录"
        android:textColor="#FFFFFF"
        android:textSize="28sp"
        android:textStyle="bold" />

    <!-- 主要询问文本 -->
    <TextView
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:text="确定要退出登录吗？"
        android:textColor="#FFFFFF"
        android:textSize="20sp" />

    <!-- 警告区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30dp"
        android:orientation="horizontal">

        <!-- 警告图标 -->
        <ImageView
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/ic_userinfo_dialog_balance_waring" />

        <!-- 警告文本区域 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 注意文本 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="注意：退出后将清理以下数据："
                android:textColor="#FFFFFF"
                android:textSize="20sp" />

            <!-- 数据列表 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="• 登录状态"
                android:textColor="#FFFFFF"
                android:textSize="20sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="• 用户信息"
                android:textColor="#FFFFFF"
                android:textSize="20sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:text="• 应用缓存"
                android:textColor="#FFFFFF"
                android:textSize="20sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="• 所有本地数据"
                android:textColor="#FFFFFF"
                android:textSize="20sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:orientation="horizontal">

        <!-- 取消按钮 -->
        <TextView
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:background="@drawable/bg_userinfo_dialog_logout_btn"
            android:paddingHorizontal="20dp"
            android:paddingVertical="5dp"
            android:text="取消"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <!-- 确定退出按钮 -->
        <TextView
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_userinfo_dialog_logout_btn"
            android:paddingHorizontal="20dp"
            android:paddingVertical="5dp"
            android:text="确定退出"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

    </LinearLayout>

</LinearLayout>
