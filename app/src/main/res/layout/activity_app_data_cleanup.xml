<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/background_base">

    <!-- 主内容容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp">

        <!-- 顶部Loading区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="48dp">

            <!-- Loading动画 -->
            <ImageView
                android:id="@+id/iv_loading_animation"
                android:layout_width="240dp"
                android:layout_height="240dp"
                android:layout_marginBottom="32dp"
                android:src="@drawable/loading_animation"
                android:scaleType="centerInside" />

            <!-- 状态提示文字 -->
            <TextView
                android:id="@+id/tv_status_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正在清理应用数据..."
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                android:shadowColor="@color/black"
                android:shadowDx="0"
                android:shadowDy="2"
                android:shadowRadius="4" />

        </LinearLayout>

        <!-- 单条进度条容器 -->
        <LinearLayout
            android:id="@+id/layout_progress_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="40dp">

            <!-- 进度条容器 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- 左侧空白 -->
                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <!-- 进度条 -->
                <ProgressBar
                    android:id="@+id/progress_cleanup"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_weight="8"
                    android:layout_height="16dp"
                    android:max="100"
                    android:progress="10"
                    android:progressDrawable="@drawable/custom_progress_bar" />

                <!-- 右侧空白 -->
                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

            </LinearLayout>

            <!-- 进度百分比文字 -->
            <TextView
                android:id="@+id/tv_progress_percent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="0%"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:layout_marginTop="12dp"
                android:shadowColor="@color/black"
                android:shadowDx="0"
                android:shadowDy="1"
                android:shadowRadius="2" />

        </LinearLayout>

        <!-- 底部完成提示区域 -->
        <LinearLayout
            android:id="@+id/layout_completion_hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="24dp"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_completion_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/text_cleanup_complete_small"
                android:scaleType="centerInside" />

        </LinearLayout>

    </LinearLayout>

</FrameLayout>
