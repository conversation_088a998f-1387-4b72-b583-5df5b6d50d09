<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景图片 -->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/bg_login" />

    <!-- 主要内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部倒计时框架 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/bg_countdown"
            android:gravity="center"
            android:paddingHorizontal="50dp"
            android:paddingVertical="15dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="无操作"
                android:textColor="@color/white"
                android:textSize="24sp" />

            <TextView
                android:id="@+id/tv_countdown_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:gravity="center"
                android:text="180"
                android:textColor="#FFE361"
                android:textSize="24sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="秒后将回到首页"
                android:textColor="@color/white"
                android:textSize="24sp" />

        </LinearLayout>

        <!-- 用户信息区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="80dp"
            android:background="@drawable/bg_userinfo_info"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="80dp"
            android:paddingVertical="50dp">


            <!-- 左侧用户信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <!-- 用户头像 -->
                <FrameLayout
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:layout_marginBottom="20dp">

                    <ImageView
                        android:id="@+id/iv_user_avatar"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@drawable/circle_background"
                        android:contentDescription="用户头像"
                        android:scaleType="centerCrop"
                        android:src="@drawable/user_info_avatar" />

                    <!-- 头像加载指示器 -->
                    <ProgressBar
                        android:id="@+id/pb_avatar_loading"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_gravity="center"
                        android:indeterminateTint="@color/lock_accent_gold"
                        android:visibility="gone" />

                </FrameLayout>

                <!-- 用户昵称 -->
                <TextView
                    android:id="@+id/tv_user_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp"
                    android:text="用户昵称"
                    android:textColor="@color/white"
                    android:textSize="28sp"
                    android:textStyle="bold" />

                <!-- 用户余额 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/balance_background"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/user_info_coin" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="余额："
                        android:textColor="@color/text_secondary"
                        android:textSize="18sp" />

                    <TextView
                        android:id="@+id/tv_user_balance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0"
                        android:textColor="@color/lock_accent_gold"
                        android:textSize="22sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=" 元"
                        android:textColor="@color/text_secondary"
                        android:textSize="18sp" />

                    <!-- 刷新按钮 -->
                    <ImageView
                        android:id="@+id/iv_refresh_balance"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginStart="12dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="刷新余额"
                        android:padding="4dp"
                        android:src="@drawable/user_info_refresh" />

                </LinearLayout>
            </LinearLayout>

            <!-- 右侧二维码 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="150dp"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <FrameLayout
                    android:layout_width="220dp"
                    android:layout_height="220dp"
                    android:layout_gravity="center"
                    android:background="@drawable/qr_card_background"
                    android:padding="16dp">

                    <ImageView
                        android:id="@+id/iv_qr_code"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="fitCenter" />

                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="30dp"
                    android:text="微信扫码"
                    android:textColor="@color/white"
                    android:textSize="24sp" />
            </LinearLayout>

        </LinearLayout>


        <!-- 占位空间 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">
            <!-- 余额不足提示区域 -->
            <LinearLayout
                android:id="@+id/layout_balance_warning"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="40dp"
                android:background="@drawable/bg_userinfo_dialog_balance"
                android:gravity="center"
                android:paddingHorizontal="50dp"
                android:paddingVertical="20dp"
                android:visibility="gone">

                <View
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:layout_marginTop="3dp"
                    android:background="@drawable/ic_userinfo_dialog_balance_waring" />

                <TextView
                    android:id="@+id/tv_balance_warning_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:gravity="center"
                    android:text="余额不足，请在小程序充值"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </FrameLayout>

        <TextView
            android:id="@+id/btn_start_playing"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/bg_userinfo_start"
            android:paddingHorizontal="80dp"
            android:paddingVertical="40dp"
            android:text="开始租借平板"
            android:textColor="@color/white"
            android:textSize="36sp"
            android:textStyle="bold" />

        <!-- 退出登录按钮 -->
        <TextView
            android:id="@+id/btn_logout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="16dp"
            android:background="@drawable/bg_login_back"
            android:gravity="center"
            android:paddingHorizontal="20dp"
            android:paddingVertical="5dp"
            android:text="退出登录"
            android:textColor="@color/white"
            android:textSize="30sp" />

    </LinearLayout>

</FrameLayout>
