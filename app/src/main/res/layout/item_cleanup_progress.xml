<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="@drawable/bg_cleanup_item"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="4dp">

    <!-- 应用图标占位 -->
    <ImageView
        android:id="@+id/iv_app_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="12dp"
        android:src="@drawable/ic_launcher_foreground"
        android:scaleType="centerCrop" />

    <!-- 应用信息和进度 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 应用名称 -->
        <TextView
            android:id="@+id/tv_app_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="应用名称"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end" />

        <!-- 包名 -->
        <TextView
            android:id="@+id/tv_package_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="com.example.app"
            android:textColor="@color/white"
            android:textSize="11sp"
            android:alpha="0.7"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_marginTop="2dp" />

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/progress_cleanup"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="6dp"
            android:max="100"
            android:progress="0"
            android:progressDrawable="@drawable/custom_progress_bar" />

    </LinearLayout>

    <!-- 状态文本 -->
    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:text="等待清理"
        android:textColor="@color/white"
        android:textSize="12sp"
        android:minWidth="60dp"
        android:gravity="center" />

</LinearLayout>
