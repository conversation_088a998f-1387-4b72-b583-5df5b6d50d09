<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:background="#80000000"
    android:padding="32dp">

    <!-- 对话框容器 -->
    <LinearLayout
        android:id="@+id/dialog_container"
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/dialog_background"
        android:padding="24dp"
        android:elevation="8dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="设置"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- 密码输入框 -->
        <EditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:hint="请输入管理员密码"
            android:inputType="number"
            android:maxLines="1"
            android:background="@drawable/edittext_background"
            android:padding="12dp"
            android:textSize="16sp"
            android:textColor="#333333"
            android:textColorHint="#999999"
            android:layout_marginBottom="24dp"
            android:imeOptions="actionDone" />

        <!-- 按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <!-- 取消按钮 -->
            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:text="取消"
                android:textSize="14sp"
                android:textColor="#666666"
                android:background="@drawable/button_cancel_background"
                android:layout_marginEnd="12dp" />

            <!-- 确定按钮 -->
            <Button
                android:id="@+id/btn_confirm"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:text="确定"
                android:textSize="14sp"
                android:textColor="#FFFFFF"
                android:background="@drawable/button_confirm_background" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
