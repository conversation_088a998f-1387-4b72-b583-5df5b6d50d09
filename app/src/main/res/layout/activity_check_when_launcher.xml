<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent">

    <!-- 半透明背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80000000" />

    <!-- 加载提示容器 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_loading_container"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="32dp">

        <!-- 加载动画 -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:indeterminateTint="#FFFFFF" />

        <!-- 加载文本 -->
        <TextView
            android:id="@+id/tvLoadingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/verifying_app_launch"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 应用信息 -->
        <TextView
            android:id="@+id/tvAppInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="#CCFFFFFF"
            android:textSize="14sp"
            android:visibility="gone" />

        <!-- 取消按钮 -->
        <Button
            android:id="@+id/btnCancel"
            android:layout_width="wrap_content"
            android:layout_height="36dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/bg_cancel_button"
            android:text="@string/cancel"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:paddingHorizontal="24dp" />

    </LinearLayout>

</RelativeLayout>
