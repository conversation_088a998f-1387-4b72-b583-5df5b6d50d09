<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:gravity="center"
        android:text="@string/home_app_title"
        android:textSize="24sp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/btn_start_countdown_test"
        android:layout_width="200dp"
        android:visibility="gone"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/start_countdown_test"
        tools:visibility="visible"
        android:textSize="16sp" />

    <Button
        android:id="@+id/btn_whitelist_manager"
        android:layout_width="200dp"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/whitelist_manager"
        android:textSize="14sp" />

    <Button
        android:id="@+id/btnStartGovernanceState"
        android:layout_width="250dp"
        android:paddingVertical="20dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="18dp"
        android:text="进入管控状态"
        android:textSize="14sp" />


    <Button
        android:id="@+id/btnFinish"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="返回"
        android:textSize="14sp" />

    <Button
        android:id="@+id/btn_check_update"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="检查更新"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="@string/status_ready"
        android:textSize="14sp" />


    <TextView
        android:id="@+id/tv_accessibility_status"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="@string/checking_accessibility_service"
        android:textColor="#666666"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/tvVersion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textSize="12sp" />

    <TextView
        android:id="@+id/btn_reset_device_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:layout_marginBottom="8dp"
        android:backgroundTint="#F5F5F5"
        android:text="重置设备码"
        android:textColor="#999999"
        android:textSize="10sp" />
</LinearLayout>
