<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 阴影 -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="4dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/lock_shadow_soft" />
            <corners android:radius="12dp" />
        </shape>
    </item>

    <!-- 主体背景 -->
    <item android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/lock_surface_glass_light" />
            <corners android:radius="10dp" />
            <stroke
                android:width="1dp"
                android:color="@color/lock_accent_orange" />
        </shape>
    </item>

    <!-- 内部发光效果 -->
    <item android:bottom="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="@color/lock_glow_gold"
                android:centerColor="@android:color/transparent"
                android:endColor="@android:color/transparent" />
            <corners android:radius="10dp" />
        </shape>
    </item>
</layer-list>
