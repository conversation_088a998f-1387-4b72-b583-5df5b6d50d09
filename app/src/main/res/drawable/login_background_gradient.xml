<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 主渐变背景 - 温暖的蓝色渐变 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="@color/lock_primary_deep"
                android:centerColor="@color/lock_primary_mid"
                android:endColor="@color/lock_primary_light" />
        </shape>
    </item>

    <!-- 径向渐变叠加 - 增加温暖感 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="radial"
                android:gradientRadius="800dp"
                android:centerX="0.3"
                android:centerY="0.2"
                android:startColor="@color/lock_glow_gold"
                android:endColor="@android:color/transparent" />
        </shape>
    </item>

    <!-- 底部渐变 - 增加深度 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="@android:color/transparent"
                android:endColor="@color/lock_shadow_soft" />
        </shape>
    </item>
</layer-list>
