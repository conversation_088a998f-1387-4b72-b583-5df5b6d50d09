<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 按下状态 -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#66FF4444" />
            <corners android:radius="18dp" />
            <stroke
                android:width="1dp"
                android:color="#FFFF4444" />
        </shape>
    </item>
    
    <!-- 正常状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#44FF4444" />
            <corners android:radius="18dp" />
            <stroke
                android:width="1dp"
                android:color="#CCFF4444" />
        </shape>
    </item>
    
</selector>
