<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/lock_glow_blue">
    <item>
        <selector>
            <!-- 按下状态 -->
            <item android:state_pressed="true">
                <layer-list>
                    <!-- 外阴影 -->
                    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="3dp">
                        <shape android:shape="rectangle">
                            <solid android:color="@color/lock_shadow_strong" />
                            <corners android:radius="28dp" />
                        </shape>
                    </item>
                    <!-- 按钮主体 - 渐变 -->
                    <item android:bottom="2dp">
                        <shape android:shape="rectangle">
                            <gradient
                                android:type="linear"
                                android:angle="135"
                                android:startColor="#3182CE"
                                android:endColor="#2B6CB0" />
                            <corners android:radius="26dp" />
                        </shape>
                    </item>
                    <!-- 内阴影 -->
                    <item android:bottom="2dp">
                        <shape android:shape="rectangle">
                            <gradient
                                android:type="linear"
                                android:angle="90"
                                android:startColor="@color/lock_shadow_soft"
                                android:centerColor="@android:color/transparent"
                                android:endColor="@android:color/transparent" />
                            <corners android:radius="26dp" />
                        </shape>
                    </item>
                </layer-list>
            </item>

            <!-- 正常状态 -->
            <item>
                <layer-list>
                    <!-- 外发光 -->
                    <item android:top="3dp" android:left="3dp" android:right="3dp" android:bottom="6dp">
                        <shape android:shape="rectangle">
                            <solid android:color="@color/lock_glow_blue" />
                            <corners android:radius="30dp" />
                        </shape>
                    </item>
                    <!-- 外阴影 -->
                    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="5dp">
                        <shape android:shape="rectangle">
                            <solid android:color="@color/lock_shadow_strong" />
                            <corners android:radius="28dp" />
                        </shape>
                    </item>
                    <!-- 按钮主体 - 渐变 -->
                    <item android:bottom="3dp">
                        <shape android:shape="rectangle">
                            <gradient
                                android:type="linear"
                                android:angle="135"
                                android:startColor="#4299E1"
                                android:endColor="#3182CE" />
                            <corners android:radius="26dp" />
                        </shape>
                    </item>
                    <!-- 顶部高光 -->
                    <item android:bottom="3dp">
                        <shape android:shape="rectangle">
                            <gradient
                                android:type="linear"
                                android:angle="90"
                                android:startColor="@color/lock_glow_white"
                                android:centerColor="@android:color/transparent"
                                android:endColor="@android:color/transparent" />
                            <corners android:radius="26dp" />
                        </shape>
                    </item>
                    <!-- 边框 -->
                    <item android:bottom="3dp">
                        <shape android:shape="rectangle">
                            <solid android:color="@android:color/transparent" />
                            <corners android:radius="26dp" />
                            <stroke
                                android:width="1dp"
                                android:color="@color/lock_glow_blue" />
                        </shape>
                    </item>
                </layer-list>
            </item>
        </selector>
    </item>
</ripple>
