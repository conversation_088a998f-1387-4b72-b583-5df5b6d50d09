<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外阴影 - 大范围柔和阴影 -->
    <item android:top="4dp" android:left="4dp" android:right="4dp" android:bottom="8dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/lock_shadow_strong" />
            <corners android:radius="20dp" />
        </shape>
    </item>

    <!-- 中阴影 - 中等阴影 -->
    <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="6dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/lock_shadow_soft" />
            <corners android:radius="18dp" />
        </shape>
    </item>

    <!-- 毛玻璃主体 -->
    <item android:bottom="4dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/lock_surface_glass_strong" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="@color/lock_border_light" />
        </shape>
    </item>

    <!-- 顶部高光 -->
    <item android:bottom="4dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="@color/lock_glow_white"
                android:centerColor="@android:color/transparent"
                android:endColor="@android:color/transparent" />
            <corners android:radius="16dp" />
        </shape>
    </item>

    <!-- 边框发光效果 -->
    <item android:bottom="4dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="16dp" />
            <stroke
                android:width="1dp"
                android:color="@color/lock_border_accent" />
        </shape>
    </item>
</layer-list>
