<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 外圈发光 -->
    <item>
        <rotate
            android:fromDegrees="0"
            android:toDegrees="360"
            android:pivotX="50%"
            android:pivotY="50%"
            android:duration="2000"
            android:repeatCount="infinite">
            <shape
                android:shape="ring"
                android:innerRadiusRatio="2.5"
                android:thicknessRatio="12"
                android:useLevel="false">
                <gradient
                    android:type="sweep"
                    android:startColor="@color/lock_glow_gold"
                    android:centerColor="@color/lock_accent_gold"
                    android:endColor="@android:color/transparent" />
            </shape>
        </rotate>
    </item>

    <!-- 内圈主体 -->
    <item>
        <rotate
            android:fromDegrees="0"
            android:toDegrees="-360"
            android:pivotX="50%"
            android:pivotY="50%"
            android:duration="1500"
            android:repeatCount="infinite">
            <shape
                android:shape="ring"
                android:innerRadiusRatio="3"
                android:thicknessRatio="8"
                android:useLevel="false">
                <gradient
                    android:type="sweep"
                    android:startColor="@color/lock_accent_gold"
                    android:centerColor="@color/lock_accent_orange"
                    android:endColor="@android:color/transparent" />
            </shape>
        </rotate>
    </item>
</layer-list>
